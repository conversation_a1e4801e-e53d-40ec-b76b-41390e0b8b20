"""
Views for School Administrators
Handles school-level administration functionality
"""
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth.models import User
from django.db import transaction
from django.http import HttpResponse, JsonResponse
from django.core.paginator import Paginator
from django.db.models import Count, Q, Avg, Max, Min
from django.utils import timezone
from .models import *
from .forms import *
from .decorators import school_admin_required, get_user_school
import zipfile
import io


@school_admin_required
def school_admin_dashboard(request):
    """School administrator dashboard"""
    try:
        school = request.user.school
    except School.DoesNotExist:
        messages.error(request, 'Please complete school setup first.')
        return redirect('school_setup')
    
    # Get school statistics
    total_students = Student.objects.filter(school=school, is_active=True).count()
    total_teachers = TeacherAssignment.objects.filter(school=school, is_active=True).values('teacher').distinct().count()
    total_classes = Class.objects.filter(school=school, is_active=True).count()
    total_subjects = Subject.objects.filter(school=school, is_active=True).count()
    
    # Recent activity
    recent_students = Student.objects.filter(school=school, is_active=True).order_by('-created_at')[:5]
    recent_scores = Score.objects.filter(
        student__school=school
    ).order_by('-created_at')[:5]
    
    # Class statistics
    class_stats = []
    for class_obj in Class.objects.filter(school=school, is_active=True):
        student_count = Student.objects.filter(current_class=class_obj, is_active=True).count()
        teacher_count = TeacherAssignment.objects.filter(
            class_assigned=class_obj, is_active=True
        ).values('teacher').distinct().count()
        
        class_stats.append({
            'class': class_obj,
            'student_count': student_count,
            'teacher_count': teacher_count,
        })
    
    context = {
        'school': school,
        'today': timezone.now().date(),
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_classes': total_classes,
        'total_subjects': total_subjects,
        'recent_students': recent_students,
        'recent_scores': recent_scores,
        'class_stats': class_stats,
    }
    
    return render(request, 'core/school_admin/dashboard.html', context)


@school_admin_required
def manage_teachers(request):
    """Manage teachers for the school"""
    school = get_user_school(request.user)

    # Get only teachers who have assignments in this school
    teacher_assignments = TeacherAssignment.objects.filter(
        school=school,
        is_active=True
    ).select_related('teacher', 'teacher__profile', 'class_assigned', 'subject')

    # Group assignments by teacher
    teachers_dict = {}
    for assignment in teacher_assignments:
        teacher = assignment.teacher
        if teacher not in teachers_dict:
            teachers_dict[teacher] = {
                'teacher': teacher,
                'profile': getattr(teacher, 'profile', None),
                'assignments': [],
                'is_class_teacher': False,
                'assignment_count': 0,
            }

        teachers_dict[teacher]['assignments'].append(assignment)
        teachers_dict[teacher]['assignment_count'] += 1

        if assignment.is_class_teacher:
            teachers_dict[teacher]['is_class_teacher'] = True

    # Convert to list for template
    teachers_data = list(teachers_dict.values())

    context = {
        'school': school,
        'teachers_data': teachers_data,
    }

    return render(request, 'core/school_admin/teachers/list.html', context)


@school_admin_required
def add_teacher(request):
    """Add a new teacher to the school"""
    school = get_user_school(request.user)

    if request.method == 'POST':
        form = TeacherInvitationForm(request.POST)
        if form.is_valid():
            # Generate unique username based on school
            base_username = f"{school.name.lower().replace(' ', '')[:6]}_{form.cleaned_data['first_name'].lower()}_{form.cleaned_data['last_name'].lower()}"
            username = base_username
            counter = 1

            # Ensure username is unique
            while User.objects.filter(username=username).exists():
                username = f"{base_username}_{counter}"
                counter += 1

            # Generate a more secure default password
            import secrets
            import string
            default_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))

            # Create user account
            user = User.objects.create_user(
                username=username,
                email=form.cleaned_data['email'],
                first_name=form.cleaned_data['first_name'],
                last_name=form.cleaned_data['last_name'],
                password=default_password
            )

            # Create profile
            profile = form.save(commit=False)
            profile.user = user
            profile.user_type = 'teacher'
            profile.save()

            # Teacher is created without any assignments
            # Admin must manually assign classes and subjects later
            messages.info(
                request,
                f'Teacher {user.get_full_name()} has been added successfully. '
                f'Please assign them to classes and subjects using the "Assign Teacher" option.'
            )

            # Send welcome email with login details
            try:
                from django.core.mail import send_mail
                from django.template.loader import render_to_string
                from django.conf import settings

                # Prepare email context with school branding
                email_context = {
                    'user': user,
                    'school': school,
                    'username': username,
                    'password': default_password,
                    'login_url': request.build_absolute_uri('/login/'),
                    'school_logo_url': school.logo.url if school.logo else None,
                    'admin_user': request.user,
                }

                # Render email templates
                subject = f'Welcome to {school.name} - Your Teacher Account Created'
                html_message = render_to_string('core/emails/teacher_welcome.html', email_context)
                plain_message = render_to_string('core/emails/teacher_welcome.txt', email_context)

                send_mail(
                    subject,
                    plain_message,
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    html_message=html_message,
                    fail_silently=False,
                )
                email_sent = True

            except Exception as e:
                email_sent = False
                print(f"Failed to send welcome email: {e}")

            if email_sent:
                messages.success(
                    request,
                    f'Teacher {user.get_full_name()} has been added successfully! '
                    f'Login details have been sent to {user.email}.'
                )
            else:
                messages.success(
                    request,
                    f'Teacher {user.get_full_name()} has been added successfully! '
                    f'Username: {username} | Password: {default_password} '
                    f'(Please share these details with the teacher securely)'
                )

            return redirect('manage_teachers')
    else:
        form = TeacherInvitationForm()

    context = {
        'school': school,
        'form': form,
    }

    return render(request, 'core/school_admin/teachers/add.html', context)


@school_admin_required
def assign_teacher(request):
    """Assign teachers to classes and multiple subjects"""
    school = get_user_school(request.user)

    if request.method == 'POST':
        form = TeacherAssignmentForm(school=school, data=request.POST)
        if form.is_valid():
            teacher = form.cleaned_data['teacher']
            class_obj = form.cleaned_data['class_assigned']
            subjects = form.cleaned_data['subjects']
            is_class_teacher = form.cleaned_data['is_class_teacher']

            # If class teacher is checked, assign to all subjects
            if is_class_teacher:
                subjects = Subject.objects.filter(school=school, is_active=True)

                # Remove class teacher status from others in this class
                TeacherAssignment.objects.filter(
                    school=school,
                    class_assigned=class_obj,
                    is_class_teacher=True
                ).update(is_class_teacher=False)

            # Track assignments created
            assignments_created = []
            assignments_updated = []
            assignments_skipped = []

            for subject in subjects:
                # Check if assignment already exists
                existing = TeacherAssignment.objects.filter(
                    teacher=teacher,
                    school=school,
                    class_assigned=class_obj,
                    subject=subject
                ).first()

                if existing:
                    # Update existing assignment
                    if is_class_teacher and not existing.is_class_teacher:
                        existing.is_class_teacher = True
                        existing.save()
                        assignments_updated.append(subject.name)
                    else:
                        assignments_skipped.append(subject.name)
                else:
                    # Create new assignment
                    TeacherAssignment.objects.create(
                        teacher=teacher,
                        school=school,
                        class_assigned=class_obj,
                        subject=subject,
                        is_class_teacher=is_class_teacher
                    )
                    assignments_created.append(subject.name)

            # Build success message
            message_parts = []
            if assignments_created:
                message_parts.append(f"Assigned to {len(assignments_created)} new subjects: {', '.join(assignments_created)}")
            if assignments_updated:
                message_parts.append(f"Updated {len(assignments_updated)} existing assignments: {', '.join(assignments_updated)}")
            if assignments_skipped:
                message_parts.append(f"Skipped {len(assignments_skipped)} existing assignments: {', '.join(assignments_skipped)}")

            if message_parts:
                role_text = " as Class Teacher" if is_class_teacher else ""
                messages.success(
                    request,
                    f'{teacher.get_full_name()} assigned to {class_obj.name}{role_text}. ' +
                    ' | '.join(message_parts)
                )
            else:
                messages.info(request, 'No new assignments were created.')

            return redirect('manage_teachers')
    else:
        form = TeacherAssignmentForm(school=school)

    # Get current assignments for display
    current_assignments = TeacherAssignment.objects.filter(
        school=school, is_active=True
    ).select_related('teacher', 'class_assigned', 'subject').order_by(
        'class_assigned__name', 'teacher__first_name', 'subject__name'
    )

    context = {
        'school': school,
        'form': form,
        'current_assignments': current_assignments,
    }

    return render(request, 'core/school_admin/teachers/assign_teacher.html', context)


@school_admin_required
def delete_subject(request, subject_id):
    """Delete a subject with safety checks"""
    school = get_user_school(request.user)
    subject = get_object_or_404(Subject, id=subject_id, school=school)

    if request.method == 'POST':
        # Check if subject has any scores or assignments
        has_scores = Score.objects.filter(subject=subject).exists()
        has_assignments = TeacherAssignment.objects.filter(subject=subject).exists()

        if has_scores:
            messages.error(
                request,
                f'Cannot delete "{subject.name}" because it has student scores. '
                'Please remove all scores first or contact support.'
            )
            return redirect('subjects_list')

        if has_assignments:
            # Remove teacher assignments for this subject
            TeacherAssignment.objects.filter(subject=subject).delete()
            messages.info(request, f'Removed teacher assignments for "{subject.name}".')

        # Delete the subject
        subject_name = subject.name
        subject.delete()

        messages.success(request, f'Subject "{subject_name}" has been deleted successfully.')
        return redirect('subjects_list')

    # Get related data for confirmation
    scores_count = Score.objects.filter(subject=subject).count()
    assignments_count = TeacherAssignment.objects.filter(subject=subject).count()

    context = {
        'school': school,
        'subject': subject,
        'scores_count': scores_count,
        'assignments_count': assignments_count,
        'can_delete': scores_count == 0,
    }

    return render(request, 'core/school_admin/subjects/delete_subject.html', context)





@school_admin_required
def reset_user_password(request, user_id):
    """Reset password for a teacher or other user"""
    school = get_user_school(request.user)

    try:
        user = User.objects.get(id=user_id)

        # Check if user belongs to this school (for teachers)
        if hasattr(user, 'profile') and user.profile.user_type == 'teacher':
            # Verify teacher is associated with this school through active assignments
            teacher_assignments = TeacherAssignment.objects.filter(
                teacher=user,
                school=school,
                is_active=True
            )
            if not teacher_assignments.exists():
                messages.error(request, 'You can only reset passwords for teachers in your school.')
                return redirect('manage_teachers')

        if request.method == 'POST':
            # Generate new secure password
            import secrets
            import string
            new_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(10))

            # Reset password
            user.set_password(new_password)
            user.save()

            # Send email with new password
            try:
                from django.core.mail import send_mail
                from django.template.loader import render_to_string
                from django.conf import settings

                # Prepare email context
                email_context = {
                    'user': user,
                    'school': school,
                    'new_password': new_password,
                    'login_url': request.build_absolute_uri('/login/'),
                    'school_logo_url': school.logo.url if school.logo else None,
                }

                # Render email template
                subject = f'Password Reset - {school.name}'
                html_message = render_to_string('core/emails/password_reset.html', email_context)
                plain_message = render_to_string('core/emails/password_reset.txt', email_context)

                send_mail(
                    subject,
                    plain_message,
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    html_message=html_message,
                    fail_silently=False,
                )

                messages.success(
                    request,
                    f'Password has been reset for {user.get_full_name()}. '
                    f'New login credentials have been sent to {user.email}.'
                )

            except Exception as e:
                messages.warning(
                    request,
                    f'Password has been reset for {user.get_full_name()}, but email could not be sent. '
                    f'New password: {new_password} (Please share this securely with the user)'
                )
                print(f"Failed to send password reset email: {e}")

            return redirect('manage_teachers')

        context = {
            'school': school,
            'target_user': user,
        }

        return render(request, 'core/school_admin/users/reset_password.html', context)

    except User.DoesNotExist:
        messages.error(request, 'User not found.')
        return redirect('manage_teachers')


@school_admin_required
def teacher_detail(request, teacher_id):
    """View teacher details and assignments"""
    school = get_user_school(request.user)
    teacher = get_object_or_404(User, id=teacher_id)

    # Get teacher assignments for this school
    assignments = TeacherAssignment.objects.filter(
        teacher=teacher, school=school, is_active=True
    ).select_related('class_assigned', 'subject')

    # Calculate statistics
    unique_classes_count = assignments.values('class_assigned').distinct().count()
    class_teacher_count = assignments.filter(is_class_teacher=True).count()

    # Calculate total students taught
    total_students = 0
    for assignment in assignments:
        students_in_class = Student.objects.filter(
            current_class=assignment.class_assigned, is_active=True
        ).count()
        total_students += students_in_class

    context = {
        'school': school,
        'teacher': teacher,
        'assignments': assignments,
        'unique_classes_count': unique_classes_count,
        'class_teacher_count': class_teacher_count,
        'total_students': total_students,
    }

    return render(request, 'core/school_admin/teacher_detail.html', context)


@school_admin_required
def teacher_edit(request, teacher_id):
    """Edit teacher information"""
    school = get_user_school(request.user)
    teacher = get_object_or_404(User, id=teacher_id)

    # Verify teacher belongs to this school through active assignments
    teacher_assignments = TeacherAssignment.objects.filter(
        teacher=teacher,
        school=school,
        is_active=True
    )

    if not teacher_assignments.exists():
        messages.error(request, 'Teacher not found in your school or has no active assignments.')
        return redirect('manage_teachers')

    if request.method == 'POST':
        # Update teacher information
        teacher.first_name = request.POST.get('first_name', teacher.first_name)
        teacher.last_name = request.POST.get('last_name', teacher.last_name)
        teacher.email = request.POST.get('email', teacher.email)
        teacher.save()

        # Update profile if exists
        try:
            profile = teacher.profile
            profile.phone_number = request.POST.get('phone_number', profile.phone_number)
            profile.employee_id = request.POST.get('employee_id', profile.employee_id)
            profile.save()
        except:
            pass

        messages.success(request, f'Teacher {teacher.get_full_name()} updated successfully!')
        return redirect('teacher_detail', teacher_id=teacher.id)

    context = {
        'school': school,
        'teacher': teacher,
    }

    return render(request, 'core/school_admin/teacher_edit.html', context)


@school_admin_required
def teacher_reset_password(request, teacher_id):
    """Reset teacher password"""
    if request.method == 'POST':
        school = get_user_school(request.user)
        teacher = get_object_or_404(User, id=teacher_id)

        # Verify teacher belongs to this school
        if not TeacherAssignment.objects.filter(teacher=teacher, school=school).exists():
            return JsonResponse({'success': False, 'error': 'Teacher not found in your school.'})

        # Reset password
        teacher.set_password('teacher123')
        teacher.save()

        return JsonResponse({'success': True, 'message': 'Password reset successfully!'})

    return JsonResponse({'success': False, 'error': 'Invalid request method.'})


@school_admin_required
def teacher_toggle_status(request, teacher_id):
    """Toggle teacher active status"""
    if request.method == 'POST':
        school = get_user_school(request.user)
        teacher = get_object_or_404(User, id=teacher_id)

        # Verify teacher belongs to this school
        if not TeacherAssignment.objects.filter(teacher=teacher, school=school).exists():
            return JsonResponse({'success': False, 'error': 'Teacher not found in your school.'})

        import json
        data = json.loads(request.body)
        teacher.is_active = data.get('is_active', not teacher.is_active)
        teacher.save()

        status = 'activated' if teacher.is_active else 'deactivated'
        return JsonResponse({'success': True, 'message': f'Teacher {status} successfully!'})

    return JsonResponse({'success': False, 'error': 'Invalid request method.'})


@school_admin_required
def teacher_remove(request, teacher_id):
    """Remove teacher from school"""
    school = get_user_school(request.user)
    teacher = get_object_or_404(User, id=teacher_id)

    # Verify teacher belongs to this school
    assignments = TeacherAssignment.objects.filter(teacher=teacher, school=school)
    if not assignments.exists():
        messages.error(request, 'Teacher not found in your school.')
        return redirect('manage_teachers')

    if request.method == 'POST':
        # Remove all assignments for this teacher in this school
        assignments.delete()

        # If teacher has no other assignments, deactivate the account
        if not TeacherAssignment.objects.filter(teacher=teacher).exists():
            teacher.is_active = False
            teacher.save()

        messages.success(request, f'Teacher {teacher.get_full_name()} removed successfully!')
        return redirect('manage_teachers')

    context = {
        'school': school,
        'teacher': teacher,
        'assignments': assignments,
    }

    return render(request, 'core/school_admin/teacher_remove.html', context)


@school_admin_required
def debug_teacher_assignments(request):
    """Debug view to check teacher assignments for troubleshooting"""
    school = get_user_school(request.user)

    # Get all teachers
    all_teachers = User.objects.filter(
        profile__user_type='teacher',
        is_active=True
    ).select_related('profile')

    # Get all assignments for this school
    all_assignments = TeacherAssignment.objects.filter(
        school=school
    ).select_related('teacher', 'class_assigned', 'subject')

    # Debug information
    debug_info = {
        'school': school,
        'total_teachers_system': all_teachers.count(),
        'total_assignments_school': all_assignments.count(),
        'active_assignments_school': all_assignments.filter(is_active=True).count(),
        'teachers_with_assignments': all_assignments.values('teacher').distinct().count(),
        'all_teachers': all_teachers,
        'all_assignments': all_assignments,
    }

    # Teachers without assignments in this school
    teachers_with_assignments = all_assignments.values_list('teacher_id', flat=True)
    teachers_without_assignments = all_teachers.exclude(id__in=teachers_with_assignments)
    debug_info['teachers_without_assignments'] = teachers_without_assignments

    return render(request, 'core/school_admin/debug_teachers.html', debug_info)


@school_admin_required
def school_reports(request):
    """School-wide reports and analytics"""
    school = get_user_school(request.user)
    
    # Get all classes
    classes = Class.objects.filter(school=school, is_active=True)
    
    # Filter by class if specified
    selected_class_id = request.GET.get('class_id')
    if selected_class_id:
        selected_class = get_object_or_404(Class, id=selected_class_id, school=school)
        students = Student.objects.filter(
            current_class=selected_class,
            is_active=True
        ).order_by('first_name', 'last_name')
    else:
        selected_class = None
        students = Student.objects.filter(
            school=school, is_active=True
        ).order_by('current_class__name', 'first_name', 'last_name')
    
    # Get term results
    results = StudentTermResult.objects.filter(
        student__school=school,
        term=school.current_term,
        academic_year=school.academic_year
    ).select_related('student', 'student__current_class').order_by(
        'student__current_class__name', 'position'
    )
    
    if selected_class:
        results = results.filter(student__current_class=selected_class)
    
    context = {
        'school': school,
        'classes': classes,
        'selected_class': selected_class,
        'students': students,
        'results': results,
    }
    
    return render(request, 'core/school_admin/reports.html', context)


@school_admin_required
def download_class_reports(request, class_id):
    """Download all reports for a class as ZIP file"""
    school = get_user_school(request.user)
    class_obj = get_object_or_404(Class, id=class_id, school=school)
    
    # Create ZIP file in memory
    zip_buffer = io.BytesIO()
    
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        # Get all students in the class
        students = Student.objects.filter(
            current_class=class_obj, is_active=True
        ).order_by('first_name', 'last_name')
        
        for student in students:
            # Generate individual report (this would call the PDF generation)
            # For now, we'll create a simple text file
            report_content = f"""
TERMINAL REPORT CARD
{school.name}
{school.motto}

Student: {student.full_name}
Class: {class_obj.name}
Term: {school.current_term}
Academic Year: {school.academic_year}

[Report content would be generated here]
            """.strip()
            
            # Add to ZIP
            filename = f"{student.full_name.replace(' ', '_')}_report.txt"
            zip_file.writestr(filename, report_content)
    
    zip_buffer.seek(0)
    
    # Create response
    response = HttpResponse(
        zip_buffer.getvalue(),
        content_type='application/zip'
    )
    response['Content-Disposition'] = f'attachment; filename="{class_obj.name}_reports.zip"'
    
    return response


@school_admin_required
def school_settings(request):
    """School settings and configuration"""
    school = get_user_school(request.user)

    if request.method == 'POST':
        form = SchoolSettingsForm(school=school, data=request.POST, files=request.FILES)
        if form.is_valid():
            try:
                school, settings = form.save(school)
                messages.success(request, 'School settings updated successfully!')
                return redirect('school_settings')
            except Exception as e:
                messages.error(request, f'Error saving settings: {str(e)}')
        else:
            # Display form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = SchoolSettingsForm(school=school)

    # Get all classes for the dropdown
    classes = Class.objects.filter(school=school, is_active=True).order_by('name')

    # Get all subjects for the school
    subjects = Subject.objects.filter(school=school, is_active=True).order_by('name')

    # Get report card templates
    report_templates = SystemReportCardTemplate.objects.filter(
        is_active=True
    ).order_by('name')

    context = {
        'school': school,
        'form': form,
        'classes': classes,
        'subjects': subjects,
        'report_templates': report_templates,
    }

    return render(request, 'core/school_admin/settings_enhanced.html', context)


@school_admin_required
def report_templates(request):
    """Manage report card templates for the school"""
    school = get_user_school(request.user)

    # Get all available templates
    templates = SystemReportCardTemplate.objects.filter(is_active=True).order_by('template_type', 'name')

    # Group templates by type
    templates_by_type = {}
    for template in templates:
        template_type = template.get_template_type_display()
        if template_type not in templates_by_type:
            templates_by_type[template_type] = []
        templates_by_type[template_type].append(template)

    context = {
        'school': school,
        'templates_by_type': templates_by_type,
        'current_template': school.report_template,
    }

    return render(request, 'core/school_admin/report_templates.html', context)


@school_admin_required
def select_report_template(request, template_id):
    """Select a report card template for the school"""
    school = get_user_school(request.user)
    template = get_object_or_404(SystemReportCardTemplate, id=template_id, is_active=True)

    school.report_template = template
    school.save()

    messages.success(request, f'Report template "{template.name}" has been selected for your school.')
    return redirect('report_templates')


@school_admin_required
def preview_report_template(request, template_id):
    """Preview a report card template"""
    school = get_user_school(request.user)
    template = get_object_or_404(SystemReportCardTemplate, id=template_id, is_active=True)

    # Return JSON data for AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        try:
            preview_data = {
                'name': template.name,
                'description': template.description,
                'template_type': template.get_template_type_display(),
                'design_style': template.get_design_style_display(),
                'color_scheme': template.get_color_scheme_display(),
                'features': template.get_features_list(),
                'preview_image': getattr(template, 'preview_image', None).url if getattr(template, 'preview_image', None) else None,
            }
            return JsonResponse(preview_data)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    # For regular requests, render the preview page
    # Get school settings for grading system
    school_settings = SchoolSettings.objects.filter(school=school).first()
    grading_system = school_settings.grading_system if school_settings else 'numeric'

    # Generate sample grades based on school's grading system
    def get_sample_grade_and_remark(total_score):
        if grading_system == 'ges_bece':
            if total_score >= 80:
                return '1', 'EXCELLENT'
            elif total_score >= 70:
                return '2', 'VERY GOOD'
            elif total_score >= 60:
                return '3', 'GOOD'
            elif total_score >= 50:
                return '4', 'SATISFACTORY'
            elif total_score >= 40:
                return '5', 'PASS'
            elif total_score >= 30:
                return '6', 'PASS'
            elif total_score >= 25:
                return '7', 'PASS'
            elif total_score >= 20:
                return '8', 'PASS'
            else:
                return '9', 'FAIL'
        elif grading_system == 'waec':
            if total_score >= 80:
                return 'A1', 'EXCELLENT'
            elif total_score >= 70:
                return 'B2', 'VERY GOOD'
            elif total_score >= 65:
                return 'B3', 'GOOD'
            elif total_score >= 60:
                return 'C4', 'CREDIT'
            elif total_score >= 55:
                return 'C5', 'CREDIT'
            elif total_score >= 50:
                return 'C6', 'CREDIT'
            elif total_score >= 45:
                return 'D7', 'PASS'
            elif total_score >= 40:
                return 'E8', 'PASS'
            else:
                return 'F9', 'FAIL'
        elif grading_system == 'alphabetic':
            if total_score >= 80:
                return 'A', 'EXCELLENT'
            elif total_score >= 70:
                return 'B', 'VERY GOOD'
            elif total_score >= 60:
                return 'C', 'GOOD'
            elif total_score >= 50:
                return 'D', 'SATISFACTORY'
            else:
                return 'F', 'FAIL'
        elif grading_system == 'percentage':
            return f'{total_score}%', 'EXCELLENT' if total_score >= 80 else 'GOOD' if total_score >= 60 else 'SATISFACTORY'
        else:  # numeric (1-5)
            if total_score >= 80:
                return '1', 'EXCELLENT'
            elif total_score >= 70:
                return '2', 'VERY GOOD'
            elif total_score >= 60:
                return '3', 'GOOD'
            elif total_score >= 50:
                return '4', 'SATISFACTORY'
            else:
                return '5', 'FAIL'

    # Sample data for preview with dynamic grading
    sample_subjects = [
        {'name': 'English Language', 'classwork': 28, 'exam': 65, 'total': 93},
        {'name': 'Mathematics', 'classwork': 25, 'exam': 68, 'total': 93},
        {'name': 'Science', 'classwork': 20, 'exam': 45, 'total': 65},
        {'name': 'Social Studies', 'classwork': 27, 'exam': 66, 'total': 93},
    ]

    # Add grades and remarks based on school's grading system
    for subject in sample_subjects:
        grade, remark = get_sample_grade_and_remark(subject['total'])
        subject['grade'] = grade
        subject['remarks'] = remark

    # Sample data for preview
    sample_data = {
        'school': {
            'name': school.name,
            'motto': school.motto or 'Excellence in Education',
            'address': school.address,
        },
        'student': {
            'name': 'John Doe',
            'class': 'Basic 6A',
            'admission_no': 'SCH-001',
            'term': school.current_term,
            'academic_year': school.academic_year,
            'position': '4th',
            'total_students': '25',
            'percentage': '83.56%',
            'total_marks': '668.5',
            'attendance': '55 OUT OF 55',
        },
        'subjects': sample_subjects,
        'conduct': 'Excellent',
        'teacher_remarks': 'John is a dedicated student who shows great potential in mathematics and science. Keep up the good work!',
        'grading_system': grading_system,
    }

    context = {
        'school': school,
        'template': template,
        'sample_data': sample_data,
    }

    return render(request, 'core/school_admin/preview_report_template.html', context)


@school_admin_required
def grading_fields_setup(request):
    """Setup grading fields for the school"""
    school = get_user_school(request.user)

    # Get existing grading fields
    grading_fields = SchoolGradingField.objects.filter(
        school=school, is_active=True
    ).order_by('order')

    # Get system grading fields for reference
    system_fields = SystemGradingField.objects.filter(is_active=True)

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'add_field':
            # Add new grading field
            name = request.POST.get('name')
            code = request.POST.get('code')
            percentage = request.POST.get('percentage')
            max_score = request.POST.get('max_score', 100)

            if name and code and percentage:
                # Get next order
                last_field = SchoolGradingField.objects.filter(school=school).order_by('-order').first()
                next_order = (last_field.order + 1) if last_field else 1

                SchoolGradingField.objects.create(
                    school=school,
                    name=name,
                    code=code,
                    percentage=int(percentage),
                    max_score=int(max_score),
                    order=next_order
                )
                messages.success(request, f'Grading field "{name}" added successfully!')
            else:
                messages.error(request, 'Please fill in all required fields.')

        elif action == 'update_field':
            # Update existing field
            field_id = request.POST.get('field_id')
            field = get_object_or_404(SchoolGradingField, id=field_id, school=school)

            field.name = request.POST.get('name', field.name)
            field.code = request.POST.get('code', field.code)
            field.percentage = int(request.POST.get('percentage', field.percentage))
            field.max_score = int(request.POST.get('max_score', field.max_score))
            field.save()

            messages.success(request, f'Grading field "{field.name}" updated successfully!')

        elif action == 'delete_field':
            # Delete field
            field_id = request.POST.get('field_id')
            field = get_object_or_404(SchoolGradingField, id=field_id, school=school)
            field.is_active = False
            field.save()

            messages.success(request, f'Grading field "{field.name}" removed successfully!')

        return redirect('grading_fields_setup')

    # Calculate total percentage
    total_percentage = sum(field.percentage for field in grading_fields)

    context = {
        'school': school,
        'grading_fields': grading_fields,
        'system_fields': system_fields,
        'total_percentage': total_percentage,
    }

    return render(request, 'core/school_admin/grading_fields.html', context)


@school_admin_required
def term_configuration(request):
    """Configure academic terms for the school"""
    school = get_user_school(request.user)

    # Get existing term configuration
    try:
        term_config = TermConfiguration.objects.get(school=school, is_active=True)
    except TermConfiguration.DoesNotExist:
        term_config = None

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'save_config':
            term = request.POST.get('term')
            academic_year = request.POST.get('academic_year')
            classwork_percentage = request.POST.get('classwork_percentage', 30)
            test_percentage = request.POST.get('test_percentage', 0)
            exam_percentage = request.POST.get('exam_percentage', 70)

            if term_config:
                term_config.term = term
                term_config.academic_year = academic_year
                term_config.classwork_percentage = int(classwork_percentage)
                term_config.test_percentage = int(test_percentage)
                term_config.exam_percentage = int(exam_percentage)
                term_config.save()
            else:
                term_config = TermConfiguration.objects.create(
                    school=school,
                    term=term,
                    academic_year=academic_year,
                    classwork_percentage=int(classwork_percentage),
                    test_percentage=int(test_percentage),
                    exam_percentage=int(exam_percentage)
                )

            # Update school's current term and academic year
            school.current_term = term
            school.academic_year = academic_year
            school.save()

            messages.success(request, 'Term configuration saved successfully!')
            return redirect('term_configuration')

    context = {
        'school': school,
        'term_config': term_config,
    }

    return render(request, 'core/school_admin/term_config.html', context)


@school_admin_required
def generate_student_report(request):
    """Generate individual student report - Step 1: Select Class"""
    school = get_user_school(request.user)

    # Get classes that have students with submitted scores
    classes_with_scores = Class.objects.filter(
        school=school,
        is_active=True,
        students__scores__is_submitted=True,
        students__scores__term=school.current_term,
        students__scores__academic_year=school.academic_year
    ).distinct().order_by('name')

    selected_class_id = request.GET.get('class_id')
    selected_class = None
    students_with_scores = []

    if selected_class_id:
        selected_class = get_object_or_404(Class, id=selected_class_id, school=school)

        # Get students who have submitted scores
        students_with_scores = Student.objects.filter(
            current_class=selected_class,
            is_active=True,
            scores__is_submitted=True,
            scores__term=school.current_term,
            scores__academic_year=school.academic_year
        ).distinct().order_by('first_name', 'last_name')

        # Add score statistics for each student
        for student in students_with_scores:
            scores = Score.objects.filter(
                student=student,
                is_submitted=True,
                term=school.current_term,
                academic_year=school.academic_year
            )
            student.total_subjects = scores.count()
            student.average_score = scores.aggregate(avg=Avg('total_score'))['avg'] or 0

    context = {
        'school': school,
        'classes_with_scores': classes_with_scores,
        'selected_class': selected_class,
        'students_with_scores': students_with_scores,
        'current_term': school.current_term,
        'academic_year': school.academic_year,
    }

    return render(request, 'core/school_admin/reports/student_report.html', context)


@school_admin_required
def bulk_student_reports(request):
    """Generate bulk student reports"""
    school = get_user_school(request.user)

    if request.method == 'POST':
        class_id = request.POST.get('class_id')
        if class_id:
            class_obj = get_object_or_404(Class, id=class_id, school=school)

            # Get students with submitted scores
            students_with_scores = Student.objects.filter(
                current_class=class_obj,
                is_active=True,
                scores__is_submitted=True,
                scores__term=school.current_term,
                scores__academic_year=school.academic_year
            ).distinct()

            if students_with_scores.exists():
                # Generate ZIP file with all reports
                from reports.views import generate_bulk_reports_zip
                zip_response = generate_bulk_reports_zip(students_with_scores, school)
                return zip_response
            else:
                messages.error(request, f'No students with submitted scores found in {class_obj.name}')
        else:
            messages.error(request, 'Please select a class')

    # Get classes that have students with submitted scores
    classes_with_scores = Class.objects.filter(
        school=school,
        is_active=True,
        students__scores__is_submitted=True,
        students__scores__term=school.current_term,
        students__scores__academic_year=school.academic_year
    ).distinct().order_by('name')

    context = {
        'school': school,
        'classes_with_scores': classes_with_scores,
        'current_term': school.current_term,
        'academic_year': school.academic_year,
    }

    return render(request, 'core/school_admin/reports/bulk_reports.html', context)


@school_admin_required
def generate_individual_report(request, student_id):
    """Generate individual student report with preview"""
    school = get_user_school(request.user)
    student = get_object_or_404(Student, id=student_id, school=school)

    # Get all scores for this student (both submitted and draft for school admin view)
    scores = Score.objects.filter(
        student=student,
        term=school.current_term,
        academic_year=school.academic_year
    ).select_related('subject').order_by('subject__name')

    if not scores.exists():
        messages.error(request, f'No scores found for {student.full_name}. Teachers need to enter scores first.')
        return redirect('generate_student_report')

    # Calculate student statistics
    total_score = sum(score.total_score for score in scores)
    average_score = total_score / scores.count() if scores.count() > 0 else 0

    # Get or create term result
    term_result, created = StudentTermResult.objects.get_or_create(
        student=student,
        term=school.current_term,
        academic_year=school.academic_year,
        defaults={
            'total_score': total_score,
            'average_score': average_score,
            'subjects_count': scores.count(),
        }
    )

    if not created:
        # Update existing result
        term_result.total_score = total_score
        term_result.average_score = average_score
        term_result.subjects_count = scores.count()
        term_result.save()

    # Calculate position
    term_result.calculate_position()

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'download_pdf':
            # Generate PDF
            from reports.views import generate_student_report_pdf_professional
            from django.http import HttpResponse

            pdf_content = generate_student_report_pdf_professional(student, term_result, scores, school)

            # Create HTTP response with PDF content
            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{student.full_name}_report.pdf"'
            return response
        elif action == 'preview':
            # Show preview
            pass

    # Convert student profile picture to base64 for better display
    student_photo_base64 = None
    if student.profile_picture:
        try:
            import base64
            from django.conf import settings
            import os

            # Get the full file path
            photo_path = os.path.join(settings.MEDIA_ROOT, student.profile_picture.name)
            if os.path.exists(photo_path):
                with open(photo_path, 'rb') as image_file:
                    image_data = image_file.read()
                    student_photo_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            print(f"Error converting student photo to base64: {e}")
            student_photo_base64 = None

    # Convert school logo to base64 for better display
    school_logo_base64 = None
    if school.logo:
        try:
            import base64
            from django.conf import settings
            import os

            # Get the full file path
            logo_path = os.path.join(settings.MEDIA_ROOT, school.logo.name)
            if os.path.exists(logo_path):
                with open(logo_path, 'rb') as image_file:
                    image_data = image_file.read()
                    school_logo_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            print(f"Error converting school logo to base64: {e}")
            school_logo_base64 = None

    context = {
        'school': school,
        'student': student,
        'scores': scores,
        'term_result': term_result,
        'current_term': school.current_term,
        'academic_year': school.academic_year,
        'total_subjects': scores.count(),
        'average_score': average_score,
        'student_photo_base64': student_photo_base64,
        'school_logo_base64': school_logo_base64,
    }

    return render(request, 'core/school_admin/reports/individual_report.html', context)


@school_admin_required
def student_performance_analysis(request):
    """Student performance analysis"""
    school = get_user_school(request.user)

    # Get performance data
    students = Student.objects.filter(school=school, is_active=True)
    term_results = StudentTermResult.objects.filter(
        student__school=school,
        term=school.current_term,
        academic_year=school.academic_year
    ).select_related('student', 'student__current_class').order_by('-average_score')

    # Calculate statistics
    if term_results.exists():
        average_percentage = term_results.aggregate(avg=Avg('average_score'))['avg'] or 0
        highest_class_average = 0
        excellent_performers = term_results.filter(average_score__gte=80).count()

        # Calculate class performance
        classes = Class.objects.filter(school=school, is_active=True)
        class_performance = []

        for class_obj in classes:
            class_results = term_results.filter(student__current_class=class_obj)
            if class_results.exists():
                class_avg = class_results.aggregate(avg=Avg('average_score'))['avg'] or 0
                class_highest = class_results.aggregate(max=Max('average_score'))['max'] or 0
                class_lowest = class_results.aggregate(min=Min('average_score'))['min'] or 0

                class_performance.append({
                    'class': class_obj,
                    'student_count': class_results.count(),
                    'average': class_avg,
                    'highest': class_highest,
                    'lowest': class_lowest,
                })

                if class_avg > highest_class_average:
                    highest_class_average = class_avg

        # Grade distribution
        grade_distribution = {}
        for result in term_results:
            if result.average_score >= 80:
                grade = 'A'
            elif result.average_score >= 70:
                grade = 'B'
            elif result.average_score >= 60:
                grade = 'C'
            elif result.average_score >= 50:
                grade = 'D'
            else:
                grade = 'F'

            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
    else:
        average_percentage = 0
        highest_class_average = 0
        excellent_performers = 0
        class_performance = []
        grade_distribution = {}

    context = {
        'school': school,
        'students': students,
        'term_results': term_results,
        'average_percentage': average_percentage,
        'highest_class_average': highest_class_average,
        'excellent_performers': excellent_performers,
        'class_performance': class_performance,
        'grade_distribution': grade_distribution,
    }

    return render(request, 'core/school_admin/reports/performance_analysis.html', context)


@school_admin_required
def class_performance_report(request):
    """Class performance report"""
    school = get_user_school(request.user)

    classes = Class.objects.filter(school=school, is_active=True).order_by('name')
    class_performance = []

    # Performance counters
    excellent_classes = 0
    very_good_classes = 0
    good_classes = 0
    poor_classes = 0

    for class_obj in classes:
        students_count = Student.objects.filter(current_class=class_obj, is_active=True).count()

        # Get class results
        class_results = StudentTermResult.objects.filter(
            student__current_class=class_obj,
            term=school.current_term,
            academic_year=school.academic_year
        )

        if class_results.exists():
            avg_score = class_results.aggregate(avg=Avg('average_score'))['avg'] or 0
            highest_score = class_results.aggregate(max=Max('average_score'))['max'] or 0
            lowest_score = class_results.aggregate(min=Min('average_score'))['min'] or 0
        else:
            avg_score = 0
            highest_score = 0
            lowest_score = 0

        # Count performance categories
        if avg_score >= 80:
            excellent_classes += 1
        elif avg_score >= 70:
            very_good_classes += 1
        elif avg_score >= 60:
            good_classes += 1
        else:
            poor_classes += 1

        class_performance.append({
            'class': class_obj,
            'class_id': class_obj.id,
            'class_name': class_obj.name,
            'class_teacher': None,  # You can add this if you have class teacher relationship
            'student_count': students_count,
            'average_score': round(avg_score, 2),
            'highest_score': round(highest_score, 2),
            'lowest_score': round(lowest_score, 2)
        })

    # Sort by average score descending
    class_performance.sort(key=lambda x: x['average_score'], reverse=True)

    # Calculate percentages
    total_classes = len(class_performance)
    excellent_classes_percent = (excellent_classes / total_classes * 100) if total_classes > 0 else 0
    very_good_classes_percent = (very_good_classes / total_classes * 100) if total_classes > 0 else 0
    good_classes_percent = (good_classes / total_classes * 100) if total_classes > 0 else 0
    poor_classes_percent = (poor_classes / total_classes * 100) if total_classes > 0 else 0

    context = {
        'school': school,
        'class_performance': class_performance,
        'current_term': school.current_term,
        'academic_year': school.academic_year,
        'excellent_classes': excellent_classes,
        'very_good_classes': very_good_classes,
        'good_classes': good_classes,
        'poor_classes': poor_classes,
        'excellent_classes_percent': excellent_classes_percent,
        'very_good_classes_percent': very_good_classes_percent,
        'good_classes_percent': good_classes_percent,
        'poor_classes_percent': poor_classes_percent,
        'subject_performance': [],  # Add this if you want subject performance data
    }

    return render(request, 'core/school_admin/reports/class_performance.html', context)


@school_admin_required
def class_ranking_report(request):
    """Class ranking report"""
    school = get_user_school(request.user)

    classes = Class.objects.filter(school=school, is_active=True).order_by('name')

    context = {
        'school': school,
        'classes': classes,
    }

    return render(request, 'core/school_admin/reports/class_ranking.html', context)


@school_admin_required
def subject_analysis_report(request):
    """Subject analysis report"""
    school = get_user_school(request.user)

    subjects = Subject.objects.filter(school=school, is_active=True).order_by('name')

    context = {
        'school': school,
        'subjects': subjects,
    }

    return render(request, 'core/school_admin/reports/subject_analysis.html', context)


@school_admin_required
def school_summary_report(request):
    """School summary report"""
    school = get_user_school(request.user)

    # Get summary statistics
    total_students = Student.objects.filter(school=school, is_active=True).count()
    total_classes = Class.objects.filter(school=school, is_active=True).count()
    total_subjects = Subject.objects.filter(school=school, is_active=True).count()
    total_teachers = TeacherAssignment.objects.filter(school=school, is_active=True).values('teacher').distinct().count()

    context = {
        'school': school,
        'total_students': total_students,
        'total_classes': total_classes,
        'total_subjects': total_subjects,
        'total_teachers': total_teachers,
    }

    return render(request, 'core/school_admin/reports/school_summary.html', context)


@school_admin_required
def manage_students(request):
    """Enhanced student management with bulk upload"""
    school = get_user_school(request.user)

    # Import form at the beginning
    from .forms import BulkStudentUploadForm

    # Initialize upload_form
    upload_form = BulkStudentUploadForm(school=school)

    # Handle bulk upload
    if request.method == 'POST' and 'bulk_upload' in request.POST:
        upload_form = BulkStudentUploadForm(school=school, data=request.POST, files=request.FILES)

        if upload_form.is_valid():
            try:
                students_data, errors = upload_form.process_upload(school)

                if errors:
                    for error in errors:
                        messages.error(request, error)
                else:
                    # Create students
                    created_count = 0
                    for student_data in students_data:
                        # Check if student already exists (case-insensitive)
                        existing = Student.objects.filter(
                            school=school,
                            first_name__iexact=student_data['first_name'],
                            last_name__iexact=student_data['last_name'],
                            current_class=student_data['current_class']
                        ).first()

                        if not existing:
                            Student.objects.create(**student_data)
                            created_count += 1
                        else:
                            # Log duplicate for user awareness
                            messages.warning(request, f"Duplicate skipped: {student_data['first_name']} {student_data['last_name']} already exists in {student_data['current_class'].name}")

                    messages.success(request, f'Successfully created {created_count} students.')
                    return redirect('manage_students')

            except Exception as e:
                messages.error(request, f'Upload failed: {str(e)}')

    # Get all students
    students = Student.objects.filter(school=school).select_related('current_class').order_by('current_class__name', 'first_name', 'last_name')

    # Get classes for filtering
    classes = Class.objects.filter(school=school, is_active=True).order_by('name')

    # Filter by class if specified
    selected_class = request.GET.get('class')
    if selected_class:
        try:
            class_obj = Class.objects.get(id=selected_class, school=school)
            students = students.filter(current_class=class_obj)
        except Class.DoesNotExist:
            pass

    context = {
        'school': school,
        'students': students,
        'classes': classes,
        'selected_class': selected_class,
        'upload_form': upload_form,
    }

    return render(request, 'core/school_admin/manage_students.html', context)


@school_admin_required
def download_student_template(request):
    """Download CSV template for bulk student upload"""
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="student_upload_template.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'first_name', 'last_name', 'gender', 'parent_name', 'parent_phone'
    ])

    # Add sample data
    writer.writerow([
        'John', 'Doe', 'M', 'Jane Doe', '0244123456'
    ])
    writer.writerow([
        'Mary', 'Smith', 'F', 'Robert Smith', '0244654321'
    ])

    return response


@school_admin_required
def teacher_performance_report(request):
    """Teacher performance report"""
    school = get_user_school(request.user)

    teachers = TeacherAssignment.objects.filter(school=school, is_active=True).values('teacher').distinct()

    context = {
        'school': school,
        'teachers': teachers,
    }

    return render(request, 'core/school_admin/reports/teacher_performance.html', context)


@school_admin_required
def attendance_report(request):
    """Attendance report"""
    school = get_user_school(request.user)

    classes = Class.objects.filter(school=school, is_active=True).order_by('name')

    context = {
        'school': school,
        'classes': classes,
    }

    return render(request, 'core/school_admin/reports/attendance.html', context)
