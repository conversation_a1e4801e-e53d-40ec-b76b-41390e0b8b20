{% extends 'base.html' %}

{% block title %}All Schools - Site Administration{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
    <!-- Mobile-First Header -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
            <div class="flex flex-col space-y-4 py-4 sm:py-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">All Schools</h1>
                        <p class="text-sm sm:text-base text-gray-600">Manage all schools in the system</p>
                    </div>
                    <a href="{% url 'site_admin_dashboard' %}"
                       class="sm:hidden p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </a>
                </div>
                <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <a href="{% url 'site_admin_analytics' %}"
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center active:scale-95">
                        <i data-lucide="bar-chart-3" class="w-4 h-4 inline mr-2"></i>
                        Analytics
                    </a>
                    <a href="{% url 'site_admin_dashboard' %}"
                       class="hidden sm:inline-flex items-center justify-center bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile-First Main Content -->
    <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <!-- Mobile-First Schools List -->
        {% if schools %}
        <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-4 sm:px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i data-lucide="building" class="w-5 h-5 mr-2 text-purple-600"></i>
                        Registered Schools
                    </h3>
                    <span class="text-sm text-gray-500">{{ schools|length }} school{{ schools|length|pluralize }}</span>
                </div>
            </div>

            <!-- Mobile Card Layout (default) -->
            <div class="block lg:hidden">
                <div class="divide-y divide-gray-200">
                    {% for school in schools %}
                    <div class="p-4 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start space-x-3">
                            <!-- School Logo/Avatar -->
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                {% if school.logo %}
                                    <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-10 h-10 rounded-lg object-cover">
                                {% else %}
                                    <span class="text-white font-bold text-sm">{{ school.name|slice:":2" }}</span>
                                {% endif %}
                            </div>

                            <!-- School Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-base font-semibold text-gray-900 truncate">{{ school.name }}</h3>
                                        <p class="text-sm text-gray-600 truncate">{{ school.location|default:"No location" }}</p>
                                        {% if school.phone_number %}
                                        <p class="text-xs text-gray-500">{{ school.phone_number }}</p>
                                        {% endif %}
                                    </div>

                                    <!-- Actions Dropdown -->
                                    <div class="relative ml-2">
                                        <button onclick="toggleSchoolDropdown('school-{{ forloop.counter }}')"
                                                class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                                            <i data-lucide="more-vertical" class="w-4 h-4"></i>
                                        </button>
                                        <div id="school-{{ forloop.counter }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                            <div class="py-1">
                                                <a href="{% url 'site_admin_school_detail' school.id %}"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                                                    View Details
                                                </a>
                                                <a href="/admin/core/school/{{ school.id }}/change/" target="_blank"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                                    Edit in Django
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- School Stats -->
                                <div class="mt-3 grid grid-cols-3 gap-3">
                                    <div class="text-center bg-blue-50 rounded-lg p-2">
                                        <p class="text-lg font-bold text-blue-900">{{ school.student_count|default:0 }}</p>
                                        <p class="text-xs text-blue-600">Students</p>
                                    </div>
                                    <div class="text-center bg-green-50 rounded-lg p-2">
                                        <p class="text-lg font-bold text-green-900">{{ school.teacher_count|default:0 }}</p>
                                        <p class="text-xs text-green-600">Teachers</p>
                                    </div>
                                    <div class="text-center bg-purple-50 rounded-lg p-2">
                                        <p class="text-lg font-bold text-purple-900">{{ school.class_count|default:0 }}</p>
                                        <p class="text-xs text-purple-600">Classes</p>
                                    </div>
                                </div>

                                <!-- Administrator Info -->
                                {% if school.admin %}
                                <div class="mt-3 p-2 bg-gray-50 rounded-lg">
                                    <p class="text-xs text-gray-600">Administrator:</p>
                                    <p class="text-sm font-medium text-gray-900">{{ school.admin.get_full_name }}</p>
                                    <p class="text-xs text-gray-500">{{ school.admin.email }}</p>
                                </div>
                                {% endif %}

                                <!-- Status -->
                                <div class="mt-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <div class="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                                        Active
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Desktop Table Layout (hidden on mobile) -->
            <div class="hidden lg:block">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">School</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Administrator</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teachers</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for school in schools %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if school.logo %}
                                    <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-10 h-10 rounded-lg object-cover">
                                    {% else %}
                                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="building" class="w-5 h-5 text-purple-600"></i>
                                    </div>
                                    {% endif %}
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ school.name }}</div>
                                        <div class="text-sm text-gray-500">{{ school.current_term }} {{ school.academic_year }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ school.admin.get_full_name|default:school.admin.username }}</div>
                                <div class="text-sm text-gray-500">{{ school.admin.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ school.phone_number|default:"--" }}</div>
                                <div class="text-sm text-gray-500">{{ school.address|truncatechars:30|default:"--" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ school.student_count }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ school.teacher_count }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {{ school.class_count }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{% url 'site_admin_school_detail' school.id %}" 
                                       class="text-purple-600 hover:text-purple-900" title="View Details">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                    </a>
                                    <a href="{% url 'site_admin_user_detail' school.admin.id %}" 
                                       class="text-blue-600 hover:text-blue-900" title="View Admin">
                                        <i data-lucide="user" class="w-4 h-4"></i>
                                    </a>
                                    <a href="{% url 'site_admin_school_detail' school.id %}"
                                       class="text-green-600 hover:text-green-900" title="Manage">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- School Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Schools</p>
                        <p class="text-2xl font-bold text-gray-900">{{ schools|length }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% for school in schools %}{{ school.student_count|add:0 }}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="user-check" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Teachers</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% for school in schools %}{{ school.teacher_count|add:0 }}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="school" class="w-6 h-6 text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Classes</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {% for school in schools %}{{ school.class_count|add:0 }}{% empty %}0{% endfor %}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <!-- No Schools -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="building" class="w-8 h-8 text-gray-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Schools Registered</h3>
            <p class="text-gray-500 mb-6">No schools have been registered in the system yet.</p>
            <a href="{% url 'site_admin_dashboard' %}" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                Back to Dashboard
            </a>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">Manage Users</h4>
                        <p class="text-sm text-gray-600">View all system users</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_users' %}" class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                        View All Users
                    </a>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="bar-chart-3" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">System Analytics</h4>
                        <p class="text-sm text-gray-600">View system statistics</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_analytics' %}" class="w-full bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                        View Analytics
                    </a>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="server" class="w-6 h-6 text-gray-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">System Settings</h4>
                        <p class="text-sm text-gray-600">Configure system</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_system_settings' %}" class="w-full bg-gray-600 text-white text-center py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                        System Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Mobile dropdown functionality
    function toggleSchoolDropdown(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        const allDropdowns = document.querySelectorAll('[id^="school-"]');

        // Close all other dropdowns
        allDropdowns.forEach(d => {
            if (d.id !== dropdownId) {
                d.classList.add('hidden');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('hidden');
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('[id^="school-"]');
        const isDropdownButton = event.target.closest('[onclick*="toggleSchoolDropdown"]');

        if (!isDropdownButton) {
            dropdowns.forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });

    // Initialize Lucide icons
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
    });
</script>
{% endblock %}
