<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Smart Terminal Report System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .info-panel {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.25);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .animate-pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-5 rounded-full animate-pulse-slow"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-3 rounded-full animate-float"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-5 rounded-full animate-pulse-slow"></div>
    </div>

    <!-- Main Container -->
    <div class="relative z-10 w-full max-w-6xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center min-h-screen py-8">

            <!-- Left Panel - Information -->
            <div class="hidden lg:block space-y-8">
                <!-- Logo and Title -->
                <div class="text-center lg:text-left">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-15 rounded-2xl mb-6 backdrop-blur-sm">
                        <i data-lucide="graduation-cap" class="w-8 h-8 text-white"></i>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-3">Smart Terminal</h1>
                    <p class="text-white text-opacity-90 text-xl">School Report Management System</p>
                </div>

                <!-- Features -->
                <div class="space-y-6">
                    <div class="info-panel rounded-2xl p-6 shadow-lg">
                        <h3 class="text-white font-bold mb-4 flex items-center text-lg">
                            <i data-lucide="users" class="w-6 h-6 mr-3 text-blue-200"></i>
                            Multi-Role Access
                        </h3>
                        <ul class="text-white space-y-3 text-base">
                            <li class="flex items-start">
                                <span class="text-blue-200 mr-2 mt-1">•</span>
                                <span><strong class="text-blue-100">Site Admins:</strong> System management & templates</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-blue-200 mr-2 mt-1">•</span>
                                <span><strong class="text-blue-100">School Admins:</strong> School setup & teacher management</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-blue-200 mr-2 mt-1">•</span>
                                <span><strong class="text-blue-100">Teachers:</strong> Grade entry & class reports</span>
                            </li>
                        </ul>
                    </div>

                    <div class="info-panel rounded-2xl p-6 shadow-lg">
                        <h3 class="text-white font-bold mb-4 flex items-center text-lg">
                            <i data-lucide="file-text" class="w-6 h-6 mr-3 text-green-200"></i>
                            Report Features
                        </h3>
                        <ul class="text-white space-y-3 text-base">
                            <li class="flex items-start">
                                <span class="text-green-200 mr-2 mt-1">•</span>
                                <span>Professional PDF report cards</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-green-200 mr-2 mt-1">•</span>
                                <span>Automated grade calculations</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-green-200 mr-2 mt-1">•</span>
                                <span>Performance analytics</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-green-200 mr-2 mt-1">•</span>
                                <span>Bulk report generation</span>
                            </li>
                        </ul>
                    </div>

                    <div class="info-panel rounded-2xl p-6 shadow-lg">
                        <h3 class="text-white font-bold mb-4 flex items-center text-lg">
                            <i data-lucide="shield-check" class="w-6 h-6 mr-3 text-purple-200"></i>
                            Security & Support
                        </h3>
                        <ul class="text-white space-y-3 text-base">
                            <li class="flex items-start">
                                <span class="text-purple-200 mr-2 mt-1">•</span>
                                <span>Secure role-based access control</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-purple-200 mr-2 mt-1">•</span>
                                <span>Data backup & restore</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-purple-200 mr-2 mt-1">•</span>
                                <span>Professional support available</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-purple-200 mr-2 mt-1">•</span>
                                <span>Regular system updates</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Login Form -->
            <div class="w-full max-w-md mx-auto lg:mx-0">
                <!-- Mobile Logo -->
                <div class="text-center mb-8 lg:hidden">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-15 rounded-2xl mb-4 backdrop-blur-sm">
                        <i data-lucide="graduation-cap" class="w-8 h-8 text-white"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-white mb-2">Smart Terminal</h1>
                    <p class="text-white text-opacity-90">School Report System</p>
                </div>

                <!-- Login Form -->
                <div class="glass-effect rounded-2xl p-8 shadow-2xl">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-semibold text-gray-800 mb-2">Welcome Back</h2>
                        <p class="text-gray-600">Sign in to your account</p>
                    </div>

                    <form method="post" class="space-y-5">
                        {% csrf_token %}

                        <!-- Username Field -->
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-lucide="user" class="w-4 h-4 inline mr-2"></i>
                                Username
                            </label>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                required
                                class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                placeholder="Enter your username"
                            >
                        </div>

                        <!-- Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-lucide="lock" class="w-4 h-4 inline mr-2"></i>
                                Password
                            </label>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                placeholder="Enter your password"
                            >
                        </div>

                        <!-- Error Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                                    <div class="flex items-center">
                                        <i data-lucide="alert-circle" class="w-5 h-5 mr-2 text-red-500"></i>
                                        {{ message }}
                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <!-- Login Button -->
                        <button
                            type="submit"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105"
                        >
                            <i data-lucide="log-in" class="w-5 h-5 inline mr-2"></i>
                            Sign In to Dashboard
                        </button>

                        <!-- Forgot Password Link -->
                        <div class="text-center mt-4">
                            <a href="{% url 'password_reset' %}" class="text-sm text-blue-600 hover:text-blue-700 hover:underline transition-colors">
                                <i data-lucide="help-circle" class="w-4 h-4 inline mr-1"></i>
                                Forgot your password?
                            </a>
                        </div>
                    </form>

                    <!-- Quick Help -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="text-center">
                            <p class="text-sm text-gray-600 mb-3">Need help accessing your account?</p>
                            <div class="flex justify-center space-x-4 text-xs">
                                <span class="text-blue-600">Contact Administrator</span>
                                <span class="text-gray-400">|</span>
                                <span class="text-blue-600">System Guide</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-6">
                    <p class="text-white text-opacity-60 text-sm">
                        © 2024 Smart Terminal Report System
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Auto-focus username field
        document.getElementById('username').focus();
        
        // Add subtle animations
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', function() {
                const button = form.querySelector('button[type="submit"]');
                button.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 inline mr-2 animate-spin"></i>Signing In...';
                button.disabled = true;
                lucide.createIcons();
            });
        });
    </script>
</body>
</html>
