<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Smart Terminal Report System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-5 rounded-full"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-3 rounded-full"></div>
    </div>

    <!-- Main Container -->
    <div class="relative z-10 w-full max-w-md mx-auto">
        <!-- Logo -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-15 rounded-2xl mb-4 backdrop-blur-sm">
                <i data-lucide="key" class="w-8 h-8 text-white"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Reset Password</h1>
            <p class="text-white text-opacity-90">Enter your email to receive reset instructions</p>
        </div>

        <!-- Reset Form -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-2">Forgot Your Password?</h2>
                <p class="text-gray-600">No worries! Enter your email address and we'll send you a link to reset your password.</p>
            </div>

            <form method="post" class="space-y-5">
                {% csrf_token %}

                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                        Email Address
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                        placeholder="Enter your email address"
                    >
                </div>

                <!-- Error Messages -->
                {% if form.errors %}
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <p class="text-red-600 text-sm">{{ error }}</p>
                            {% endfor %}
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Submit Button -->
                <button
                    type="submit"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105"
                >
                    <i data-lucide="send" class="w-5 h-5 inline mr-2"></i>
                    Send Reset Instructions
                </button>
            </form>

            <!-- Back to Login -->
            <div class="text-center mt-6 pt-6 border-t border-gray-200">
                <a href="{% url 'login' %}" class="text-sm text-blue-600 hover:text-blue-700 hover:underline transition-colors">
                    <i data-lucide="arrow-left" class="w-4 h-4 inline mr-1"></i>
                    Back to Login
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-6">
            <p class="text-white text-opacity-60 text-sm">
                © 2024 Smart Terminal Report System
            </p>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Auto-focus email field
        document.getElementById('email').focus();
        
        // Add form submission animation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', function() {
                const button = form.querySelector('button[type="submit"]');
                button.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 inline mr-2 animate-spin"></i>Sending...';
                button.disabled = true;
                lucide.createIcons();
            });
        });
    </script>
</body>
</html>
