# Email Configuration Guide

## Overview

The email system has been restructured to separate technical SMTP configuration from school-level email preferences. This ensures that school administrators don't need to deal with complex SMTP settings while still allowing them to control their email notifications.

## Architecture

### Site Admin (Superuser) Level
- **SMTP Configuration**: Technical email server settings
- **Location**: Django Admin → Site Settings
- **Access**: Only superusers/site administrators

### School Admin Level  
- **Email Preferences**: Control which notifications to send
- **Location**: School Admin Dashboard → Settings → Email Notifications
- **Access**: School administrators

## SMTP Configuration (Site Admin Only)

Site administrators configure SMTP settings in Django Admin under "Site Settings":

- **SMTP Host**: Email server address (e.g., smtp.gmail.com)
- **SMTP Port**: Server port (usually 587 for TLS)
- **Email User**: Email address for sending emails
- **Email Password**: Email password or app password
- **Use TLS/SSL**: Encryption settings

## School Email Preferences

School administrators can control:

- **Welcome Emails**: Send welcome emails to new teachers
- **Password Reset Emails**: Send password reset notifications
- **Grade Notifications**: Send grade submission confirmations

## Email Branding

All emails are sent with school-specific branding:

- **From Address**: "School Name <site-smtp-email>"
- **Subject**: "School Name - Email Subject"
- **Content**: Includes school logo and information
- **Templates**: School-branded email templates

## Benefits

1. **Simplified Management**: School admins don't need SMTP knowledge
2. **Centralized Infrastructure**: Site admin manages email infrastructure
3. **School Branding**: Each school's emails are properly branded
4. **Security**: SMTP credentials managed centrally
5. **Scalability**: Easy to add new schools without SMTP setup

## Migration

Existing SMTP settings have been moved to Site Settings. Run the initialization command:

```bash
python manage.py init_site_settings
```

This will:
1. Create Site Settings with current SMTP configuration
2. Remove SMTP fields from school settings
3. Update email sending to use centralized SMTP with school branding

## Email Templates

Email templates are located in `templates/core/emails/`:

- `teacher_welcome.html/txt`: Welcome email for new teachers
- `password_reset.html/txt`: Password reset notifications
- `grade_notification.html/txt`: Grade submission confirmations

All templates receive school context for proper branding.

## Troubleshooting

### Emails Not Sending
1. Check Site Settings in Django Admin
2. Verify SMTP credentials are correct
3. Check email server settings (TLS/SSL)
4. Review Django logs for error messages

### School Branding Issues
1. Ensure school has logo uploaded
2. Check email template context
3. Verify school information is complete

### Permission Issues
1. Only superusers can access Site Settings
2. School admins can only modify notification preferences
3. Check user permissions and roles
