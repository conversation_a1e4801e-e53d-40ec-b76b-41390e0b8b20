<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set New Password - Smart Terminal Report System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-5 rounded-full"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-3 rounded-full"></div>
    </div>

    <!-- Main Container -->
    <div class="relative z-10 w-full max-w-md mx-auto">
        <!-- Logo -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-15 rounded-2xl mb-4 backdrop-blur-sm">
                <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Set New Password</h1>
            <p class="text-white text-opacity-90">Create a secure password for your account</p>
        </div>

        <!-- Password Reset Form -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            {% if validlink %}
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-semibold text-gray-800 mb-2">Create New Password</h2>
                    <p class="text-gray-600">Please enter your new password below.</p>
                </div>

                <form method="post" class="space-y-5">
                    {% csrf_token %}

                    <!-- New Password Field -->
                    <div>
                        <label for="new_password1" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="lock" class="w-4 h-4 inline mr-2"></i>
                            New Password
                        </label>
                        <input
                            type="password"
                            id="new_password1"
                            name="new_password1"
                            required
                            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                            placeholder="Enter your new password"
                        >
                    </div>

                    <!-- Confirm Password Field -->
                    <div>
                        <label for="new_password2" class="block text-sm font-medium text-gray-700 mb-2">
                            <i data-lucide="lock" class="w-4 h-4 inline mr-2"></i>
                            Confirm New Password
                        </label>
                        <input
                            type="password"
                            id="new_password2"
                            name="new_password2"
                            required
                            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                            placeholder="Confirm your new password"
                        >
                    </div>

                    <!-- Password Requirements -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"></i>
                            <div class="text-sm text-blue-800">
                                <p class="font-medium mb-1">Password Requirements:</p>
                                <ul class="list-disc list-inside space-y-1 text-blue-700">
                                    <li>At least 8 characters long</li>
                                    <li>Cannot be too similar to your personal information</li>
                                    <li>Cannot be a commonly used password</li>
                                    <li>Cannot be entirely numeric</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    {% if form.errors %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <p class="text-red-600 text-sm">{{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Submit Button -->
                    <button
                        type="submit"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105"
                    >
                        <i data-lucide="check" class="w-5 h-5 inline mr-2"></i>
                        Update Password
                    </button>
                </form>
            {% else %}
                <!-- Invalid Link -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i data-lucide="x-circle" class="w-8 h-8 text-red-600"></i>
                    </div>
                    
                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">Invalid Reset Link</h2>
                    
                    <div class="text-gray-600 space-y-3 mb-6">
                        <p>This password reset link is invalid or has expired.</p>
                        <p class="text-sm">Please request a new password reset link.</p>
                    </div>

                    <a href="{% url 'password_reset' %}" 
                       class="w-full inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                        <i data-lucide="refresh-cw" class="w-5 h-5 mr-2"></i>
                        Request New Reset Link
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Footer -->
        <div class="text-center mt-6">
            <p class="text-white text-opacity-60 text-sm">
                © 2024 Smart Terminal Report System
            </p>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Auto-focus first password field
        const passwordField = document.getElementById('new_password1');
        if (passwordField) {
            passwordField.focus();
        }
        
        // Add form submission animation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function() {
                    const button = form.querySelector('button[type="submit"]');
                    button.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 inline mr-2 animate-spin"></i>Updating...';
                    button.disabled = true;
                    lucide.createIcons();
                });
            }
        });
    </script>
</body>
</html>
