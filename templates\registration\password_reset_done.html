<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Email Sent - Smart Terminal Report System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-5 rounded-full"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-3 rounded-full"></div>
    </div>

    <!-- Main Container -->
    <div class="relative z-10 w-full max-w-md mx-auto">
        <!-- Logo -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-green-500 bg-opacity-20 rounded-2xl mb-4 backdrop-blur-sm">
                <i data-lucide="mail-check" class="w-8 h-8 text-green-300"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Email Sent!</h1>
            <p class="text-white text-opacity-90">Check your inbox for reset instructions</p>
        </div>

        <!-- Success Message -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <div class="text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="check-circle" class="w-8 h-8 text-green-600"></i>
                </div>
                
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">Reset Instructions Sent</h2>
                
                <div class="text-gray-600 space-y-3 mb-6">
                    <p>We've sent password reset instructions to your email address.</p>
                    <p class="text-sm">If you don't see the email in your inbox, please check your spam folder.</p>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0"></i>
                        <div class="text-sm text-blue-800">
                            <p class="font-medium mb-1">What's next?</p>
                            <ol class="list-decimal list-inside space-y-1 text-blue-700">
                                <li>Check your email inbox</li>
                                <li>Click the reset link in the email</li>
                                <li>Create a new password</li>
                                <li>Sign in with your new password</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <a href="{% url 'login' %}" 
                       class="w-full inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                        Back to Login
                    </a>
                    
                    <button onclick="resendEmail()" 
                            class="w-full inline-flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-all duration-300">
                        <i data-lucide="refresh-cw" class="w-5 h-5 mr-2"></i>
                        Didn't receive email? Resend
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-6">
            <p class="text-white text-opacity-60 text-sm">
                © 2024 Smart Terminal Report System
            </p>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        function resendEmail() {
            // Redirect back to password reset form
            window.location.href = "{% url 'password_reset' %}";
        }
    </script>
</body>
</html>
