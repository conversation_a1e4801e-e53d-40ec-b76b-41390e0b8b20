{% extends 'base.html' %}
{% load static %}

{% block title %}School Dashboard - {{ school.name }}{% endblock %}

{% block content %}
<div class="p-3 sm:p-6 space-y-6 sm:space-y-8 animate-fade-in">
    <!-- Mobile-First Enhanced Header Section -->
    <div class="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-2xl sm:rounded-3xl shadow-2xl">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-purple-600/90"></div>

        <!-- Mobile-Optimized Decorative Elements -->
        <div class="absolute top-0 right-0 w-32 h-32 sm:w-64 sm:h-64 bg-white/10 rounded-full -translate-y-16 translate-x-16 sm:-translate-y-32 sm:translate-x-32"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 sm:w-48 sm:h-48 bg-white/5 rounded-full translate-y-12 -translate-x-12 sm:translate-y-24 sm:-translate-x-24"></div>

        <div class="relative px-4 py-6 sm:px-8 sm:py-12">
            <div class="flex flex-col space-y-4 sm:space-y-6 lg:space-y-0 lg:flex-row lg:items-center lg:justify-between">
                <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 lg:space-x-6">
                    <!-- Mobile-Optimized School Logo/Avatar -->
                    <div class="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 backdrop-blur-sm rounded-xl sm:rounded-2xl flex items-center justify-center border border-white/30 mx-auto sm:mx-0">
                        {% if school.logo %}
                            <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-12 h-12 sm:w-16 sm:h-16 rounded-lg sm:rounded-xl object-cover">
                        {% else %}
                            <i data-lucide="graduation-cap" class="w-8 h-8 sm:w-10 sm:h-10 text-white"></i>
                        {% endif %}
                    </div>

                    <div class="text-center sm:text-left">
                        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2">
                            {{ school.name|truncatechars:25 }}
                        </h1>
                        <p class="text-indigo-100 text-sm sm:text-base lg:text-lg">{{ school.motto|default:"Excellence in Education"|truncatechars:40 }}</p>
                        <div class="flex items-center justify-center sm:justify-start mt-3 space-x-3 sm:space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-indigo-100 text-xs sm:text-sm">{{ school.current_term }}</span>
                            </div>
                            <div class="text-indigo-200 text-xs sm:text-sm">{{ school.academic_year }}</div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center lg:justify-end">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl border border-white/20 px-4 py-3 sm:px-6 sm:py-4">
                        <div class="text-center">
                            <p class="text-white/80 text-xs sm:text-sm">Today</p>
                            <p class="text-white text-lg sm:text-xl font-semibold">{{ today|date:"M d, Y" }}</p>
                            <p class="text-indigo-200 text-xs">{{ today|date:"l" }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile-First Enhanced Statistics Cards -->
    <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        <!-- Students Card -->
        <div class="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl sm:rounded-3xl shadow-lg border border-blue-200/50 p-4 sm:p-6 hover:shadow-xl hover:scale-105 transition-all duration-300 active:scale-95">
            <div class="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 bg-blue-500/10 rounded-full -translate-y-8 translate-x-8 sm:-translate-y-12 sm:translate-x-12"></div>
            <div class="relative">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                    <div class="w-10 h-10 sm:w-14 sm:h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 mb-2 sm:mb-0">
                        <i data-lucide="users" class="w-5 h-5 sm:w-7 sm:h-7 text-white"></i>
                    </div>
                    <div class="text-left sm:text-right">
                        <p class="text-2xl sm:text-3xl font-bold text-blue-900">{{ total_students }}</p>
                        <p class="text-blue-600 text-xs sm:text-sm font-medium">Students</p>
                    </div>
                </div>
                <div class="flex items-center text-blue-700">
                    <i data-lucide="trending-up" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                    <span class="text-xs sm:text-sm font-medium">Active learners</span>
                </div>
            </div>
        </div>

        <!-- Teachers Card -->
        <div class="group relative overflow-hidden bg-gradient-to-br from-green-50 to-green-100 rounded-2xl sm:rounded-3xl shadow-lg border border-green-200/50 p-4 sm:p-6 hover:shadow-xl hover:scale-105 transition-all duration-300 active:scale-95">
            <div class="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 bg-green-500/10 rounded-full -translate-y-8 translate-x-8 sm:-translate-y-12 sm:translate-x-12"></div>
            <div class="relative">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                    <div class="w-10 h-10 sm:w-14 sm:h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 mb-2 sm:mb-0">
                        <i data-lucide="user-check" class="w-5 h-5 sm:w-7 sm:h-7 text-white"></i>
                    </div>
                    <div class="text-left sm:text-right">
                        <p class="text-2xl sm:text-3xl font-bold text-green-900">{{ total_teachers }}</p>
                        <p class="text-green-600 text-xs sm:text-sm font-medium">Teachers</p>
                    </div>
                </div>
                <div class="flex items-center text-green-700">
                    <i data-lucide="trending-up" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                    <span class="text-xs sm:text-sm font-medium">Active staff</span>
                </div>
            </div>
        </div>

        <!-- Classes Card -->
        <div class="group relative overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl sm:rounded-3xl shadow-lg border border-purple-200/50 p-4 sm:p-6 hover:shadow-xl hover:scale-105 transition-all duration-300 active:scale-95">
            <div class="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 bg-purple-500/10 rounded-full -translate-y-8 translate-x-8 sm:-translate-y-12 sm:translate-x-12"></div>
            <div class="relative">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                    <div class="w-10 h-10 sm:w-14 sm:h-14 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 mb-2 sm:mb-0">
                        <i data-lucide="layers" class="w-5 h-5 sm:w-7 sm:h-7 text-white"></i>
                    </div>
                    <div class="text-left sm:text-right">
                        <p class="text-2xl sm:text-3xl font-bold text-purple-900">{{ total_classes }}</p>
                        <p class="text-purple-600 text-xs sm:text-sm font-medium">Classes</p>
                    </div>
                </div>
                <div class="flex items-center text-purple-700">
                    <i data-lucide="trending-up" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                    <span class="text-xs sm:text-sm font-medium">Active classes</span>
                </div>
            </div>
        </div>

        <!-- Subjects Card -->
        <div class="group relative overflow-hidden bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl sm:rounded-3xl shadow-lg border border-orange-200/50 p-4 sm:p-6 hover:shadow-xl hover:scale-105 transition-all duration-300 active:scale-95">
            <div class="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 bg-orange-500/10 rounded-full -translate-y-8 translate-x-8 sm:-translate-y-12 sm:translate-x-12"></div>
            <div class="relative">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                    <div class="w-10 h-10 sm:w-14 sm:h-14 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 mb-2 sm:mb-0">
                        <i data-lucide="book-open" class="w-5 h-5 sm:w-7 sm:h-7 text-white"></i>
                    </div>
                    <div class="text-left sm:text-right">
                        <p class="text-2xl sm:text-3xl font-bold text-orange-900">{{ total_subjects }}</p>
                        <p class="text-orange-600 text-xs sm:text-sm font-medium">Subjects</p>
                    </div>
                </div>
                <div class="flex items-center text-orange-700">
                    <i data-lucide="trending-up" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                    <span class="text-xs sm:text-sm font-medium">Curriculum</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile-First Quick Actions -->
    <div class="bg-white rounded-2xl sm:rounded-3xl shadow-xl border border-slate-200/50 p-4 sm:p-6 lg:p-8">
        <div class="flex items-center justify-between mb-4 sm:mb-6">
            <h3 class="text-lg sm:text-xl font-bold text-slate-900 flex items-center">
                <i data-lucide="zap" class="w-5 h-5 mr-2 text-indigo-600"></i>
                Quick Actions
            </h3>
            <span class="text-xs sm:text-sm text-slate-500">Tap to navigate</span>
        </div>

        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
            <a href="{% url 'add_teacher' %}"
               class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-200 active:scale-95 group">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                    <i data-lucide="user-plus" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                </div>
                <span class="text-xs sm:text-sm font-medium text-blue-900 text-center">Add Teacher</span>
            </a>

            <a href="{% url 'student_add' %}"
               class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl hover:from-green-100 hover:to-green-200 transition-all duration-200 active:scale-95 group">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                    <i data-lucide="graduation-cap" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                </div>
                <span class="text-xs sm:text-sm font-medium text-green-900 text-center">Add Student</span>
            </a>

            <a href="{% url 'manage_classes' %}"
               class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 active:scale-95 group">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                    <i data-lucide="layers" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                </div>
                <span class="text-xs sm:text-sm font-medium text-purple-900 text-center">Classes</span>
            </a>

            <a href="{% url 'school_reports' %}"
               class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl hover:from-orange-100 hover:to-orange-200 transition-all duration-200 active:scale-95 group">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-orange-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                    <i data-lucide="file-text" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                </div>
                <span class="text-xs sm:text-sm font-medium text-orange-900 text-center">Reports</span>
            </a>

            <a href="{% url 'school_settings' %}"
               class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl hover:from-gray-100 hover:to-gray-200 transition-all duration-200 active:scale-95 group">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gray-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                    <i data-lucide="settings" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                </div>
                <span class="text-xs sm:text-sm font-medium text-gray-900 text-center">Settings</span>
            </a>

            <a href="{% url 'bulk_upload' %}"
               class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl hover:from-indigo-100 hover:to-indigo-200 transition-all duration-200 active:scale-95 group">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-indigo-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                    <i data-lucide="upload" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                </div>
                <span class="text-xs sm:text-sm font-medium text-indigo-900 text-center">Bulk Upload</span>
            </a>
        </div>
    </div>

    <!-- Recent Students & Class Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Students -->
        <div class="bg-white rounded-3xl shadow-xl border border-slate-200/50 overflow-hidden">
            <div class="px-8 py-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-slate-900">Recent Students</h3>
                        <p class="text-slate-600 mt-1">Latest registrations</p>
                    </div>
                    <a href="{% url 'manage_students' %}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-xl hover:bg-blue-700 transition-colors duration-200">
                        View all
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                    </a>
                </div>
            </div>

            {% if recent_students %}
            <div class="p-6 space-y-4">
                {% for student in recent_students %}
                <div class="flex items-center p-4 bg-slate-50 rounded-2xl hover:bg-slate-100 transition-colors duration-200">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                        <span class="text-white text-sm font-bold">{{ student.first_name.0 }}{{ student.last_name.0 }}</span>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="font-semibold text-slate-900">{{ student.full_name }}</div>
                        <div class="text-sm text-slate-600">{{ student.current_class.name }} • {{ student.gender|yesno:"Male,Female" }}</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-slate-900">{{ student.created_at|date:"M d" }}</div>
                        <div class="text-xs text-slate-500">{{ student.created_at|date:"Y" }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="p-12 text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="users" class="w-8 h-8 text-blue-600"></i>
                </div>
                <h4 class="text-lg font-semibold text-slate-900 mb-2">No students registered yet</h4>
                <p class="text-slate-600 mb-6">Start by adding your first student</p>
                <a href="{% url 'student_add' %}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition-colors duration-200">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    Add Student
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Class Overview -->
        <div class="bg-white rounded-3xl shadow-xl border border-slate-200/50 overflow-hidden">
            <div class="px-8 py-6 bg-gradient-to-r from-purple-50 to-pink-50 border-b border-slate-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-slate-900">Class Overview</h3>
                        <p class="text-slate-600 mt-1">Student distribution</p>
                    </div>
                    <a href="{% url 'classes_list' %}" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-xl hover:bg-purple-700 transition-colors duration-200">
                        Manage
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2"></i>
                    </a>
                </div>
            </div>

            {% if class_stats %}
            <div class="p-6 space-y-4">
                {% for stat in class_stats %}
                <div class="flex items-center justify-between p-4 bg-slate-50 rounded-2xl hover:bg-slate-100 transition-colors duration-200">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center">
                            <i data-lucide="users" class="w-6 h-6 text-white"></i>
                        </div>
                        <div class="ml-4">
                            <div class="font-semibold text-slate-900">{{ stat.class.name }}</div>
                            <div class="text-sm text-slate-600">{{ stat.student_count }} student{{ stat.student_count|pluralize }}, {{ stat.teacher_count }} teacher{{ stat.teacher_count|pluralize }}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-purple-600">{{ stat.student_count }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="p-12 text-center">
                <div class="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="school" class="w-8 h-8 text-purple-600"></i>
                </div>
                <h4 class="text-lg font-semibold text-slate-900 mb-2">No classes created yet</h4>
                <p class="text-slate-600 mb-6">Create your first class to get started</p>
                <a href="{% url 'class_add' %}" class="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-medium rounded-xl hover:bg-purple-700 transition-colors duration-200">
                    <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                    Add Class
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Enhanced Quick Actions -->
    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <h2 class="text-2xl font-bold text-slate-900">Quick Actions</h2>
            <p class="text-slate-600">Frequently used features</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- Add Teacher -->
            <a href="{% url 'add_teacher' %}" class="group relative overflow-hidden bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="user-plus" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Add Teacher</h3>
                    <p class="text-emerald-100 text-sm">Add new teacher</p>
                    <div class="mt-4 flex items-center text-emerald-200">
                        <span class="text-xs">Add new</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- Add Student -->
            <a href="{% url 'student_add' %}" class="group relative overflow-hidden bg-gradient-to-br from-blue-500 to-indigo-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="user-plus" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Add Student</h3>
                    <p class="text-blue-100 text-sm">Register new student</p>
                    <div class="mt-4 flex items-center text-blue-200">
                        <span class="text-xs">Register new</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- View Reports -->
            <a href="{% url 'school_reports' %}" class="group relative overflow-hidden bg-gradient-to-br from-purple-500 to-pink-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="file-text" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">View Reports</h3>
                    <p class="text-purple-100 text-sm">Student reports</p>
                    <div class="mt-4 flex items-center text-purple-200">
                        <span class="text-xs">Generate reports</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- Report Templates -->
            <a href="{% url 'report_templates' %}" class="group relative overflow-hidden bg-gradient-to-br from-orange-500 to-red-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="layout-template" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Report Templates</h3>
                    <p class="text-orange-100 text-sm">Choose design</p>
                    <div class="mt-4 flex items-center text-orange-200">
                        <span class="text-xs">Customize reports</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- Grading Fields -->
            <a href="{% url 'grading_fields_setup' %}" class="group relative overflow-hidden bg-gradient-to-br from-yellow-500 to-amber-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="calculator" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Grading Fields</h3>
                    <p class="text-yellow-100 text-sm">Setup grading</p>
                    <div class="mt-4 flex items-center text-yellow-200">
                        <span class="text-xs">Configure grades</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- Term Config -->
            <a href="{% url 'term_configuration' %}" class="group relative overflow-hidden bg-gradient-to-br from-teal-500 to-cyan-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="calendar" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Term Config</h3>
                    <p class="text-teal-100 text-sm">Academic terms</p>
                    <div class="mt-4 flex items-center text-teal-200">
                        <span class="text-xs">Manage terms</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- Settings -->
            <a href="{% url 'school_settings' %}" class="group relative overflow-hidden bg-gradient-to-br from-slate-500 to-gray-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="settings" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Settings</h3>
                    <p class="text-slate-100 text-sm">School configuration</p>
                    <div class="mt-4 flex items-center text-slate-200">
                        <span class="text-xs">Configure school</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>

            <!-- Analytics -->
            <a href="{% url 'analytics_dashboard' %}" class="group relative overflow-hidden bg-gradient-to-br from-rose-500 to-pink-600 rounded-3xl shadow-xl p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-white">
                <div class="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full -translate-y-12 translate-x-12"></div>
                <div class="relative">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i data-lucide="trending-up" class="w-7 h-7 text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold mb-2">Analytics</h3>
                    <p class="text-rose-100 text-sm">Performance insights</p>
                    <div class="mt-4 flex items-center text-rose-200">
                        <span class="text-xs">View analytics</span>
                        <i data-lucide="arrow-right" class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

<script>
    // Initialize icons after content loads
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();

        // Add some interactive animations
        const cards = document.querySelectorAll('.group');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate statistics on load
        const statNumbers = document.querySelectorAll('.text-3xl');
        statNumbers.forEach(stat => {
            const finalValue = parseInt(stat.textContent);
            let currentValue = 0;
            const increment = finalValue / 30;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    stat.textContent = finalValue;
                    clearInterval(timer);
                } else {
                    stat.textContent = Math.floor(currentValue);
                }
            }, 50);
        });
    });
</script>
{% endblock %}
