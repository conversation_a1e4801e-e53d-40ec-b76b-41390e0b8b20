<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Smart Terminal Report System{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#0f172a">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49'
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        /* Button enhancements */
        .btn-primary {
            @apply inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600
                   text-white font-semibold rounded-2xl shadow-lg hover:from-blue-700 hover:to-indigo-700
                   hover:shadow-xl transition-all duration-300 transform hover:scale-105;
        }

        .btn-secondary {
            @apply inline-flex items-center px-6 py-3 border-2 border-slate-300 text-slate-700
                   font-semibold rounded-2xl bg-white hover:bg-slate-50 hover:border-slate-400
                   transition-all duration-300;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen font-inter">
    {% if user.is_authenticated and school %}
    <!-- Modern Sidebar Navigation -->
    <div class="flex h-screen bg-white">
        <!-- Sidebar -->
        <div class="hidden lg:flex lg:flex-shrink-0">
            <div class="flex flex-col w-72">
                <div class="flex flex-col flex-grow bg-gradient-to-b from-dark-900 to-dark-800 pt-5 pb-4 overflow-y-auto">
                    <!-- Enhanced Logo Section -->
                    <div class="flex items-center flex-shrink-0 px-6 mb-8">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <div class="w-14 h-14 bg-gradient-to-br from-primary-400 via-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                                    {% if school.logo %}
                                        <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-10 h-10 rounded-xl object-cover">
                                    {% else %}
                                        <i data-lucide="graduation-cap" class="w-7 h-7 text-white"></i>
                                    {% endif %}
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-dark-800 animate-pulse"></div>
                            </div>
                            <div>
                                <h1 class="text-xl font-bold text-white leading-tight">{{ school.name|truncatechars:18 }}</h1>
                                <div class="flex items-center space-x-2 mt-1">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                                    <p class="text-xs text-blue-200 font-medium">{{ school.current_term }} • {{ school.academic_year }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <nav class="mt-5 flex-1 px-4 space-y-2">


                        {% if user.is_superuser %}
                        <!-- Site Administrator Navigation -->
                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">Site Management</p>
                        </div>

                        <a href="{% url 'site_admin_schools' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'schools' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="building" class="mr-3 h-5 w-5"></i>
                            All Schools
                        </a>

                        <a href="{% url 'site_admin_users' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'users' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                            All Users
                        </a>

                        <a href="{% url 'site_admin_analytics' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'site_analytics' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="bar-chart-3" class="mr-3 h-5 w-5"></i>
                            Site Analytics
                        </a>

                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">System</p>
                        </div>

                        <a href="/admin/" target="_blank" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                            <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                            Django Admin
                        </a>

                        <a href="{% url 'site_admin_system_settings' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'system_settings' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="server" class="mr-3 h-5 w-5"></i>
                            System Settings
                        </a>

                        <!-- System Templates Section -->
                        <div class="pt-4 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">System Templates</p>
                        </div>

                        <a href="{% url 'system_class_templates' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'class_templates' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="layers" class="mr-3 h-5 w-5"></i>
                            Class Templates
                        </a>

                        <a href="{% url 'system_subject_templates' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'subject_templates' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="book-open" class="mr-3 h-5 w-5"></i>
                            Subject Templates
                        </a>

                        <a href="{% url 'system_grading_templates' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'grading_templates' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="award" class="mr-3 h-5 w-5"></i>
                            Grading Templates
                        </a>

                        <a href="{% url 'system_report_templates' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'report_templates' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="file-text" class="mr-3 h-5 w-5"></i>
                            Report Templates
                        </a>

                        <a href="{% url 'system_terms' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'terms' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="calendar" class="mr-3 h-5 w-5"></i>
                            System Terms
                        </a>

                        <!-- System Maintenance Section -->
                        <div class="pt-4 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">System Maintenance</p>
                        </div>

                        <a href="{% url 'system_maintenance' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'maintenance' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="wrench" class="mr-3 h-5 w-5"></i>
                            System Health
                        </a>

                        {% elif user.profile.is_school_admin %}
                        <!-- School Administrator Navigation -->
                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">School Management</p>
                        </div>

                        <a href="{% url 'admin_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'admin_dashboard' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>
                            Dashboard
                        </a>

                        <a href="{% url 'school_settings' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'school_settings' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                            School Settings
                        </a>

                        <a href="{% url 'manage_teachers' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'teachers' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="user-check" class="mr-3 h-5 w-5"></i>
                            Teachers
                        </a>

                        <a href="{% url 'manage_students' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'students' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                            Students
                        </a>

                        <a href="{% url 'classes_list' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'classes_list' in request.resolver_match.url_name or 'class_add' in request.resolver_match.url_name or 'class_edit' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="school" class="mr-3 h-5 w-5"></i>
                            Manage Classes
                        </a>

                        <a href="{% url 'subjects_list' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'subjects_list' in request.resolver_match.url_name or 'subject_add' in request.resolver_match.url_name or 'subject_edit' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="book-open" class="mr-3 h-5 w-5"></i>
                            Manage Subjects
                        </a>

                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">Reports & Analytics</p>
                        </div>

                        <a href="{% url 'school_reports' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'reports' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="file-text" class="mr-3 h-5 w-5"></i>
                            All Reports
                        </a>

                        <a href="{% url 'admin_score_overview' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'score_overview' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="bar-chart-3" class="mr-3 h-5 w-5"></i>
                            Score Progress
                        </a>

                        <a href="{% url 'analytics_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'analytics' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="trending-up" class="mr-3 h-5 w-5"></i>
                            Analytics
                        </a>

                        <a href="{% url 'sms_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'sms' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="message-circle" class="mr-3 h-5 w-5"></i>
                            SMS Reports
                        </a>

                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">Configuration</p>
                        </div>

                        <a href="{% url 'grading_config_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'grading_config' in request.resolver_match.url_name or 'grading_scale' in request.resolver_match.url_name or 'grade_ranges' in request.resolver_match.url_name or 'grading_fields' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="award" class="mr-3 h-5 w-5"></i>
                            Grading System
                        </a>

                        <a href="{% url 'term_setup' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'term_setup' in request.resolver_match.url_name or 'add_term' in request.resolver_match.url_name or 'edit_term' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="calendar" class="mr-3 h-5 w-5"></i>
                            Academic Terms
                        </a>

                        <a href="{% url 'class_subject_setup' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'class_subject_setup' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="layers" class="mr-3 h-5 w-5"></i>
                            Setup Templates
                        </a>

                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">System</p>
                        </div>

                        <a href="{% url 'school_settings' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'settings' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                            School Settings
                        </a>

                        {% elif user.profile.is_teacher %}
                        <!-- Teacher Navigation -->
                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">My Teaching</p>
                        </div>

                        <a href="{% url 'teacher_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if request.resolver_match.url_name == 'teacher_dashboard' %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>
                            Dashboard
                        </a>

                        <a href="#" onclick="toggleGradeEntry()" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'score_entry' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="edit-3" class="mr-3 h-5 w-5"></i>
                            Grade Entry
                            <i data-lucide="chevron-down" class="ml-auto h-4 w-4 transition-transform duration-200" id="grade-entry-chevron"></i>
                        </a>

                        <!-- Grade Entry Submenu -->
                        <div id="grade-entry-submenu" class="hidden ml-6 space-y-1">
                            {% for assignment in user.teacher_assignments.all %}
                            {% if assignment.is_active %}
                            <a href="{% url 'teacher_score_entry' assignment.class_assigned.id assignment.subject.id %}"
                               class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-400 hover:text-white hover:bg-dark-600 transition-all duration-200">
                                <i data-lucide="edit-3" class="mr-2 h-4 w-4"></i>
                                {{ assignment.class_assigned.name }} - {{ assignment.subject.name }}
                            </a>
                            {% endif %}
                            {% endfor %}
                        </div>

                        <a href="#" onclick="toggleViewScores()" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'submitted_scores' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="eye" class="mr-3 h-5 w-5"></i>
                            View Scores
                            <i data-lucide="chevron-down" class="ml-auto h-4 w-4 transition-transform duration-200" id="view-scores-chevron"></i>
                        </a>

                        <!-- View Scores Submenu -->
                        <div id="view-scores-submenu" class="hidden ml-6 space-y-1">
                            {% for assignment in user.teacher_assignments.all %}
                            {% if assignment.is_active %}
                            <a href="{% url 'teacher_submitted_scores' assignment.class_assigned.id assignment.subject.id %}"
                               class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-400 hover:text-white hover:bg-dark-600 transition-all duration-200">
                                <i data-lucide="eye" class="mr-2 h-4 w-4"></i>
                                {{ assignment.class_assigned.name }} - {{ assignment.subject.name }}
                            </a>
                            {% endif %}
                            {% endfor %}
                        </div>

                        <a href="#" onclick="togglePastTerms()" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                            <i data-lucide="archive" class="mr-3 h-5 w-5"></i>
                            Past Terms
                            <i data-lucide="chevron-down" class="ml-auto h-4 w-4 transition-transform duration-200" id="past-terms-chevron"></i>
                        </a>

                        <!-- Past Terms Submenu -->
                        <div id="past-terms-submenu" class="hidden ml-6 space-y-1">
                            {% if user.profile.is_teacher %}
                                {% comment %}
                                Past terms will be loaded dynamically based on actual data
                                For now, showing placeholder until we have historical data
                                {% endcomment %}
                                <div class="px-3 py-2 text-xs text-slate-500">
                                    No past terms available
                                </div>
                            {% endif %}
                        </div>

                        <a href="#" onclick="toggleExportOptions()" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                            <i data-lucide="download" class="mr-3 h-5 w-5"></i>
                            Export Data
                            <i data-lucide="chevron-down" class="ml-auto h-4 w-4 transition-transform duration-200" id="export-options-chevron"></i>
                        </a>

                        <!-- Export Options Submenu -->
                        <div id="export-options-submenu" class="hidden ml-6 space-y-1">
                            {% for assignment in user.teacher_assignments.all %}
                            {% if assignment.is_active %}
                            <a href="{% url 'teacher_export_scores' assignment.class_assigned.id assignment.subject.id %}"
                               class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-400 hover:text-white hover:bg-dark-600 transition-all duration-200">
                                <i data-lucide="file-spreadsheet" class="mr-2 h-4 w-4"></i>
                                {{ assignment.class_assigned.name }} - {{ assignment.subject.name }}
                            </a>
                            {% endif %}
                            {% endfor %}
                        </div>

                        <a href="#" onclick="toggleStudentGrades()" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'student_report' in request.resolver_match.url_name or 'class_reports' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                            Student Grades
                            <i data-lucide="chevron-down" class="ml-auto h-4 w-4 transition-transform duration-200" id="student-grades-chevron"></i>
                        </a>

                        <!-- Student Grades Submenu -->
                        <div id="student-grades-submenu" class="hidden ml-6 space-y-1">
                            {% regroup user.teacher_assignments.all by class_assigned as class_groups %}
                            {% for class_group in class_groups %}
                            {% if class_group.grouper %}
                            <a href="{% url 'teacher_class_reports' class_group.grouper.id %}"
                               class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-400 hover:text-white hover:bg-dark-600 transition-all duration-200">
                                <i data-lucide="file-text" class="mr-2 h-4 w-4"></i>
                                {{ class_group.grouper.name }} Reports
                            </a>
                            {% endif %}
                            {% endfor %}
                        </div>

                        <!-- Class Teacher Management (for class teachers only) -->
                        {% with class_teacher_assignments=user.teacher_assignments.all|dictsort:"class_assigned.name" %}
                        {% regroup class_teacher_assignments by class_assigned as grouped_assignments %}
                        {% for group in grouped_assignments %}
                        {% with assignment=group.list|first %}
                        {% if assignment.is_class_teacher and assignment.is_active %}
                        <a href="{% url 'student_management' assignment.class_assigned.id %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'student_management' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="clipboard-list" class="mr-3 h-5 w-5"></i>
                            {{ assignment.class_assigned.name }} - Attendance & Comments
                        </a>
                        {% endif %}
                        {% endwith %}
                        {% endfor %}
                        {% endwith %}

                        <div class="pt-2 pb-1">
                            <p class="px-3 text-xs font-semibold text-slate-500 uppercase tracking-wider">Account</p>
                        </div>

                        <a href="{% url 'profile_edit' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200 {% if 'profile' in request.resolver_match.url_name %}bg-primary-600 text-white shadow-lg{% endif %}">
                            <i data-lucide="user" class="mr-3 h-5 w-5"></i>
                            My Profile
                        </a>

                        {% else %}
                        <!-- Default/Fallback Navigation -->
                        <a href="{% url 'profile_setup' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                            <i data-lucide="user-plus" class="mr-3 h-5 w-5"></i>
                            Complete Profile
                        </a>
                        {% endif %}
                    </nav>

                    <!-- Enhanced Theme Toggle -->
                    <div class="px-4 pb-4">
                        <button id="theme-toggle" class="flex items-center justify-between w-full p-4 bg-gradient-to-r from-dark-700 to-dark-600 rounded-2xl text-slate-300 hover:text-white hover:from-dark-600 hover:to-dark-500 transition-all duration-300 border border-dark-600 shadow-lg group">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-500/20 rounded-xl flex items-center justify-center mr-3 group-hover:bg-yellow-500/30 transition-colors duration-300">
                                    <i data-lucide="sun" class="w-5 h-5 text-yellow-400" id="theme-icon"></i>
                                </div>
                                <div>
                                    <span class="font-medium" id="theme-text">Light Mode</span>
                                    <p class="text-xs text-slate-400">Switch theme</p>
                                </div>
                            </div>
                            <div class="w-6 h-6 bg-dark-500 rounded-lg flex items-center justify-center group-hover:bg-dark-400 transition-colors duration-300">
                                <i data-lucide="chevron-right" class="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300"></i>
                            </div>
                        </button>
                    </div>

                    <!-- Enhanced User Profile -->
                    <div class="flex-shrink-0 px-4 pb-4">
                        <div class="relative bg-gradient-to-r from-dark-700 to-dark-600 rounded-2xl p-4 border border-dark-600 shadow-lg">
                            <div class="flex items-center">
                                <div class="relative">
                                    <div class="w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                                        <span class="text-white text-lg font-bold">{{ user.first_name.0|default:"U" }}{{ user.last_name.0|default:"" }}</span>
                                    </div>
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-dark-700"></div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <p class="text-sm font-semibold text-white">{{ user.first_name }} {{ user.last_name }}</p>
                                    <div class="flex items-center space-x-2 mt-1">
                                        {% if user.is_superuser %}
                                            <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                            <p class="text-xs text-red-200 font-medium">Site Administrator</p>
                                        {% elif user.profile.is_school_admin %}
                                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <p class="text-xs text-blue-200 font-medium">School Administrator</p>
                                        {% elif user.profile.is_teacher %}
                                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                                            <p class="text-xs text-green-200 font-medium">Teacher</p>
                                        {% else %}
                                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                            <p class="text-xs text-gray-200 font-medium">User</p>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button class="p-2 rounded-xl text-slate-400 hover:text-white hover:bg-dark-500 transition-all duration-200" title="Settings">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                    </button>
                                    <a href="{% url 'logout' %}" class="p-2 rounded-xl text-slate-400 hover:text-red-400 hover:bg-red-500/10 transition-all duration-200" title="Logout">
                                        <i data-lucide="log-out" class="w-4 h-4"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Mobile Header -->
            <div class="lg:hidden bg-white shadow-sm border-b border-slate-200">
                <div class="flex items-center justify-between px-4 py-3">
                    <div class="flex items-center space-x-3">
                        <button id="mobile-menu-button" class="p-2 rounded-lg text-slate-600 hover:bg-slate-100">
                            <i data-lucide="menu" class="w-5 h-5"></i>
                        </button>
                        <h1 class="text-lg font-semibold text-slate-900">{{ school.name|truncatechars:15 }}</h1>
                    </div>
                    <a href="{% url 'logout' %}" class="p-2 rounded-lg text-slate-600 hover:bg-slate-100">
                        <i data-lucide="log-out" class="w-5 h-5"></i>
                    </a>
                </div>
            </div>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900">
                <!-- Messages -->
                {% if messages %}
                    <div class="p-4 space-y-3">
                        {% for message in messages %}
                            <div class="animate-slide-up rounded-xl p-4 shadow-sm border {% if message.tags == 'error' %}bg-red-50 border-red-200 text-red-800{% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-800{% elif message.tags == 'success' %}bg-green-50 border-green-200 text-green-800{% else %}bg-blue-50 border-blue-200 text-blue-800{% endif %}">
                                <div class="flex items-center space-x-2">
                                    {% if message.tags == 'error' %}
                                        <i data-lucide="alert-circle" class="w-5 h-5 text-red-500"></i>
                                    {% elif message.tags == 'warning' %}
                                        <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-500"></i>
                                    {% elif message.tags == 'success' %}
                                        <i data-lucide="check-circle" class="w-5 h-5 text-green-500"></i>
                                    {% else %}
                                        <i data-lucide="info" class="w-5 h-5 text-blue-500"></i>
                                    {% endif %}
                                    <span class="font-medium">{{ message }}</span>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Authenticated User Content -->
                {% block content %}
                {% endblock %}
            </main>
        </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 z-50 lg:hidden hidden">
        <div class="fixed inset-0 bg-black bg-opacity-50" id="mobile-menu-backdrop"></div>
        <div class="fixed inset-y-0 left-0 w-72 bg-gradient-to-b from-dark-900 to-dark-800 shadow-xl transform -translate-x-full transition-transform duration-300" id="mobile-menu">
            <!-- Mobile Menu Content (same as sidebar) -->
            <div class="flex flex-col h-full pt-5 pb-4">
                <div class="flex items-center justify-between px-6 mb-8">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
                            <i data-lucide="graduation-cap" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-lg font-bold text-white">{{ school.name|truncatechars:20 }}</h1>
                            <p class="text-xs text-slate-400">{{ school.current_term }} {{ school.academic_year }}</p>
                        </div>
                    </div>
                    <button id="mobile-menu-close" class="p-2 rounded-lg text-slate-400 hover:text-white hover:bg-dark-700">
                        <i data-lucide="x" class="w-5 h-5"></i>
                    </button>
                </div>

                <nav class="flex-1 px-4 space-y-2">
                    <!-- Role-based navigation -->

                    {% if user.is_superuser %}
                    <!-- Site Administrator Mobile Navigation -->
                    <a href="{% url 'site_admin_schools' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="building" class="mr-3 h-5 w-5"></i>
                        All Schools
                    </a>
                    <a href="{% url 'site_admin_users' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                        All Users
                    </a>
                    <a href="/admin/" target="_blank" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                        Django Admin
                    </a>

                    {% elif user.profile.is_school_admin %}
                    <!-- School Administrator Mobile Navigation -->
                    <a href="{% url 'manage_teachers' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="user-check" class="mr-3 h-5 w-5"></i>
                        Teachers
                    </a>
                    <a href="{% url 'manage_students' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                        Students
                    </a>
                    <a href="{% url 'classes_list' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="school" class="mr-3 h-5 w-5"></i>
                        Manage Classes
                    </a>
                    <a href="{% url 'subjects_list' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="book-open" class="mr-3 h-5 w-5"></i>
                        Manage Subjects
                    </a>
                    <a href="{% url 'school_reports' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="file-text" class="mr-3 h-5 w-5"></i>
                        Reports
                    </a>
                    <a href="{% url 'grading_config_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="award" class="mr-3 h-5 w-5"></i>
                        Grading System
                    </a>
                    <a href="{% url 'term_setup' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="calendar" class="mr-3 h-5 w-5"></i>
                        Academic Terms
                    </a>
                    <a href="{% url 'class_subject_setup' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="layers" class="mr-3 h-5 w-5"></i>
                        Setup Templates
                    </a>
                    <a href="{% url 'school_settings' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                        Settings
                    </a>

                    {% elif user.profile.is_teacher %}
                    <!-- Enhanced Teacher Mobile Navigation -->
                    <a href="{% url 'teacher_dashboard' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="home" class="mr-3 h-5 w-5"></i>
                        Dashboard
                    </a>

                    <!-- Quick Grade Entry Links -->
                    {% for assignment in user.teacher_assignments.all|slice:":3" %}
                    {% if assignment.is_active %}
                    <a href="{% url 'teacher_score_entry' assignment.class_assigned.id assignment.subject.id %}"
                       class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-400 hover:text-white hover:bg-dark-600 transition-all duration-200 ml-3">
                        <i data-lucide="edit-3" class="mr-2 h-4 w-4"></i>
                        {{ assignment.class_assigned.name }} - {{ assignment.subject.name|truncatechars:15 }}
                    </a>
                    {% endif %}
                    {% endfor %}

                    <a href="{% url 'profile_edit' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="user" class="mr-3 h-5 w-5"></i>
                        Profile
                    </a>

                    {% else %}
                    <!-- Default Mobile Navigation -->
                    <a href="{% url 'profile_setup' %}" class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl text-slate-300 hover:text-white hover:bg-dark-700 transition-all duration-200">
                        <i data-lucide="user-plus" class="mr-3 h-5 w-5"></i>
                        Complete Profile
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
    {% endif %}



    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Dark mode functionality
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const themeText = document.getElementById('theme-text');

        // Check for saved theme preference or default to light mode
        const currentTheme = localStorage.getItem('theme') || 'light';

        function setTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
                themeIcon.setAttribute('data-lucide', 'moon');
                themeText.textContent = 'Dark Mode';
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.classList.remove('dark');
                themeIcon.setAttribute('data-lucide', 'sun');
                themeText.textContent = 'Light Mode';
                localStorage.setItem('theme', 'light');
            }
            lucide.createIcons(); // Refresh icons
        }

        // Set initial theme
        setTheme(currentTheme);

        // Theme toggle event listener
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const newTheme = document.documentElement.classList.contains('dark') ? 'light' : 'dark';
                setTheme(newTheme);
            });
        }

        // Mobile menu functionality
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuClose = document.getElementById('mobile-menu-close');
        const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');

        function openMobileMenu() {
            mobileMenuOverlay.classList.remove('hidden');
            setTimeout(() => {
                mobileMenu.classList.remove('-translate-x-full');
            }, 10);
        }

        function closeMobileMenu() {
            mobileMenu.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileMenuOverlay.classList.add('hidden');
            }, 300);
        }

        if (mobileMenuButton) {
            mobileMenuButton.addEventListener('click', openMobileMenu);
        }

        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', closeMobileMenu);
        }

        if (mobileMenuBackdrop) {
            mobileMenuBackdrop.addEventListener('click', closeMobileMenu);
        }

        // Auto-hide messages after 5 seconds
        setTimeout(() => {
            const messages = document.querySelectorAll('[class*="animate-slide-up"]');
            messages.forEach(message => {
                message.style.transition = 'opacity 0.5s ease-out';
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 500);
            });
        }, 5000);

        // Teacher navigation dropdown functions
        function toggleGradeEntry() {
            const submenu = document.getElementById('grade-entry-submenu');
            const chevron = document.getElementById('grade-entry-chevron');

            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                submenu.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function toggleStudentGrades() {
            const submenu = document.getElementById('student-grades-submenu');
            const chevron = document.getElementById('student-grades-chevron');

            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                submenu.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function toggleViewScores() {
            const submenu = document.getElementById('view-scores-submenu');
            const chevron = document.getElementById('view-scores-chevron');

            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                submenu.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function togglePastTerms() {
            const submenu = document.getElementById('past-terms-submenu');
            const chevron = document.getElementById('past-terms-chevron');

            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                submenu.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function toggleExportOptions() {
            const submenu = document.getElementById('export-options-submenu');
            const chevron = document.getElementById('export-options-chevron');

            if (submenu.classList.contains('hidden')) {
                submenu.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                submenu.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        // Service Worker Registration for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    }, function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>
</body>
</html>
