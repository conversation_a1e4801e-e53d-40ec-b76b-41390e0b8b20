# Django Security & Deployment Checklist

## 🔒 Security Configuration

### 1. Environment Variables (.env file)
Create a `.env` file in your project root with the following settings:

```bash
# Generate a secure secret key
python generate_secret_key.py

# Copy the generated key to your .env file
SECRET_KEY=your-generated-secret-key-here
DEBUG=False
IS_PRODUCTION=True
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
```

### 2. Database Configuration
For production, use PostgreSQL instead of SQLite:

```bash
# Install PostgreSQL adapter
pip install psycopg2-binary

# Add to .env
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

### 3. Static Files
Configure static file serving for production:

```bash
# Collect static files
python manage.py collectstatic

# Configure web server (nginx/apache) to serve static files
```

### 4. HTTPS Configuration
- Obtain SSL certificate (Let's Encrypt recommended)
- Configure web server for HTTPS
- Set `IS_PRODUCTION=True` in .env

## 🚀 Deployment Steps

### 1. Pre-deployment
```bash
# Run security check
python manage.py check --deploy

# Run tests
python manage.py test

# Create database migrations
python manage.py makemigrations
python manage.py migrate
```

### 2. Production Environment
```bash
# Install production dependencies
pip install gunicorn psycopg2-binary

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput
```

### 3. Web Server Configuration
Example nginx configuration:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location /static/ {
        alias /path/to/staticfiles/;
    }
    
    location /media/ {
        alias /path/to/media/;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 Security Warnings Resolved

The following Django security warnings have been addressed:

✅ **W004**: SECURE_HSTS_SECONDS configured for production
✅ **W008**: SECURE_SSL_REDIRECT enabled for production  
✅ **W009**: SECRET_KEY generation script provided
✅ **W012**: SESSION_COOKIE_SECURE enabled for production
✅ **W016**: CSRF_COOKIE_SECURE enabled for production
✅ **W018**: DEBUG disabled for production

## 📝 Development vs Production

### Development (DEBUG=True, IS_PRODUCTION=False)
- Relaxed security settings for easier development
- HTTP connections allowed
- Detailed error pages
- SQLite database

### Production (DEBUG=False, IS_PRODUCTION=True)
- Strict security settings
- HTTPS required
- Secure cookies
- PostgreSQL recommended
- Error logging configured

## 🛡️ Additional Security Measures

1. **Regular Updates**: Keep Django and dependencies updated
2. **Monitoring**: Set up error tracking (Sentry recommended)
3. **Backups**: Regular database backups
4. **Firewall**: Configure server firewall
5. **Rate Limiting**: Implement rate limiting for API endpoints
6. **Content Security Policy**: Consider adding CSP headers

## 🔧 Troubleshooting

If you see security warnings in development:
- This is normal and expected
- Warnings are suppressed in production when `IS_PRODUCTION=True`
- Focus on fixing warnings only when deploying to production
