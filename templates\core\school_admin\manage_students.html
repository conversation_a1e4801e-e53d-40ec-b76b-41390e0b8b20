{% extends 'base.html' %}
{% load static %}

{% block title %}Manage Students - {{ school.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- Mobile-First Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
            <div class="flex flex-col space-y-4 py-4 sm:py-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Manage Students</h1>
                        <p class="mt-1 text-xs sm:text-sm text-gray-600">Add, view, and manage students in your school</p>
                    </div>
                    <a href="{% url 'admin_dashboard' %}"
                       class="sm:hidden p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </a>
                </div>

                <!-- Mobile-Optimized Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <a href="{% url 'admin_dashboard' %}"
                       class="hidden sm:inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to Dashboard
                    </a>
                    <a href="{% url 'students_bulk_upload' %}"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 transition-colors active:scale-95">
                        <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
                        <span class="hidden sm:inline">Enhanced </span>Bulk Upload
                    </a>
                    <a href="{% url 'student_add' %}"
                       class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors active:scale-95">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Add Student
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <!-- Mobile-First Quick Bulk Upload Section -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 mb-6 sm:mb-8">
            <div class="px-4 sm:px-6 py-4 border-b border-gray-200">
                <div class="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h2 class="text-base sm:text-lg font-semibold text-gray-900 flex items-center">
                            <i data-lucide="upload" class="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-orange-600"></i>
                            Quick Bulk Upload
                        </h2>
                        <p class="mt-1 text-xs sm:text-sm text-gray-600">Simple CSV upload with class assignment</p>
                    </div>
                    <div class="flex justify-center sm:justify-end">
                        <a href="{% url 'students_bulk_upload' %}"
                           class="inline-flex items-center px-3 py-2 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100">
                            <i data-lucide="zap" class="w-4 h-4 mr-2"></i>
                            Use Enhanced Upload
                        </a>
                        <p class="mt-1 text-xs text-gray-500">With progress bar & better error handling</p>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                <form method="post" enctype="multipart/form-data" class="space-y-6">
                    {% csrf_token %}
                    <input type="hidden" name="bulk_upload" value="1">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- File Upload -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-lucide="file-text" class="w-4 h-4 inline mr-1"></i>
                                Upload CSV File
                            </label>
                            {{ upload_form.file }}
                            {% if upload_form.file.help_text %}
                            <p class="mt-2 text-xs text-gray-500">{{ upload_form.file.help_text }}</p>
                            {% endif %}
                        </div>
                        
                        <!-- Class Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-lucide="users" class="w-4 h-4 inline mr-1"></i>
                                Assign to Class
                            </label>
                            {{ upload_form.class_assigned }}
                            {% if upload_form.class_assigned.help_text %}
                            <p class="mt-2 text-xs text-gray-500">{{ upload_form.class_assigned.help_text }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <a href="{% url 'download_student_template' %}" 
                               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                Download Template
                            </a>
                            <span class="text-sm text-gray-500">Download the CSV template to see the required format</span>
                        </div>
                        
                        <button type="submit" 
                                class="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                            <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
                            Upload Students
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="p-6">
                <form method="get" class="flex items-center space-x-4">
                    <div class="flex-1">
                        <label for="class" class="block text-sm font-medium text-gray-700 mb-2">Filter by Class</label>
                        <select name="class" id="class" onchange="this.form.submit()" 
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Classes</option>
                            {% for class_obj in classes %}
                            <option value="{{ class_obj.id }}" {% if selected_class == class_obj.id|stringformat:"s" %}selected{% endif %}>
                                {{ class_obj.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    {% if selected_class %}
                    <div class="pt-6">
                        <a href="{% url 'manage_students' %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="x" class="w-4 h-4 mr-2"></i>
                            Clear Filter
                        </a>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>

        <!-- Students List -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">
                        Students List
                        <span class="ml-2 text-sm font-normal text-gray-500">({{ students.count }} students)</span>
                    </h2>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Added</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for student in students %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-blue-600">
                                                {{ student.first_name|first }}{{ student.last_name|first }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ student.full_name }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ student.student_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ student.current_class.name }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if student.gender == 'M' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i data-lucide="user" class="w-3 h-3 mr-1"></i>
                                    Male
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                    <i data-lucide="user" class="w-3 h-3 mr-1"></i>
                                    Female
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>{{ student.parent_name|default:"-" }}</div>
                                <div class="text-xs text-gray-500">{{ student.parent_phone|default:"-" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ student.created_at|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="{% url 'student_detail' student.id %}" 
                                       class="text-blue-600 hover:text-blue-900">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                    </a>
                                    <a href="#" class="text-green-600 hover:text-green-900">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i data-lucide="users" class="w-12 h-12 mx-auto mb-4 text-gray-300"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No students found</h3>
                                    <p class="text-sm">Get started by adding your first student or uploading a CSV file.</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize Lucide icons
lucide.createIcons();
</script>
{% endblock %}
