from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid
import json

# System-level configurable entities (managed by System Admin)
class SystemClass(models.Model):
    """Pre-configured classes available system-wide"""
    name = models.CharField(max_length=50, unique=True)
    level = models.CharField(max_length=20, choices=[
        ('primary', 'Primary'),
        ('jhs', 'Junior High School'),
        ('shs', 'Senior High School'),
    ])
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['level', 'order', 'name']

    def __str__(self):
        return self.name


class SystemSubject(models.Model):
    """Pre-configured subjects available system-wide"""
    name = models.Char<PERSON><PERSON>(max_length=100, unique=True)
    code = models.Char<PERSON><PERSON>(max_length=10, unique=True)
    category = models.Char<PERSON>ield(max_length=30, choices=[
        ('core', 'Core Subject'),
        ('elective', 'Elective Subject'),
        ('vocational', 'Vocational Subject'),
    ], default='core')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class SystemGradingField(models.Model):
    """Pre-configured grading fields available system-wide"""
    name = models.CharField(max_length=50, unique=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True)
    default_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.default_percentage}%)"


class SystemReportTemplate(models.Model):
    """System-level report templates"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    template_data = models.JSONField(default=dict)  # Stores template configuration
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


# SchoolSettings model moved to after School model definition


class HeroBanner(models.Model):
    """Hero banner configuration for homepage"""
    title = models.CharField(max_length=200, default="Transform Your School Reports")
    subtitle = models.CharField(max_length=300, default="The most advanced school report management system")
    description = models.TextField(
        default="Generate professional report cards, track student performance, and communicate with parents - all in one powerful platform."
    )
    image_url = models.URLField(
        blank=True,
        help_text="URL for hero banner image (optional)"
    )
    background_image = models.ImageField(
        upload_to='hero_banners/',
        blank=True,
        null=True,
        help_text="Upload custom background image"
    )
    primary_button_text = models.CharField(max_length=50, default="Start Free Trial")
    primary_button_url = models.CharField(max_length=200, default="/register/school-admin/")
    secondary_button_text = models.CharField(max_length=50, default="Sign In")
    secondary_button_url = models.CharField(max_length=200, default="/login/")

    # Display settings
    show_stats = models.BooleanField(default=True, help_text="Show statistics section")
    show_floating_cards = models.BooleanField(default=True, help_text="Show floating demo cards")

    # Background settings
    background_color = models.CharField(
        max_length=20,
        default="gradient",
        choices=[
            ('gradient', 'Gradient Background'),
            ('solid', 'Solid Color'),
            ('image', 'Background Image'),
        ]
    )
    gradient_colors = models.CharField(
        max_length=100,
        default="from-blue-900 via-blue-800 to-indigo-900",
        help_text="Tailwind CSS gradient classes"
    )

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Hero Banner"
        verbose_name_plural = "Hero Banners"

    def __str__(self):
        return f"Hero Banner - {self.title[:50]}"

    @classmethod
    def get_active_banner(cls):
        """Get the currently active hero banner"""
        return cls.objects.filter(is_active=True).first()

    @property
    def background_image_url(self):
        """Get background image URL with fallback"""
        if self.background_image:
            return self.background_image.url
        elif self.image_url:
            return self.image_url
        else:
            # Fallback to a default online image
            return "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"

    @property
    def background_style(self):
        """Get CSS background style based on settings"""
        if self.background_color == 'image' and (self.background_image or self.image_url):
            return f"background-image: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url('{self.background_image_url}'); background-size: cover; background-position: center;"
        elif self.background_color == 'gradient':
            return f"background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 25%, #7c3aed 50%, #db2777 75%, #dc2626 100%);"
        else:
            return "background: #1e3a8a;"


class UserProfile(models.Model):
    """Extended user profile for teachers and administrators"""
    USER_TYPES = [
        ('system_admin', 'System Administrator'),
        ('school_admin', 'School Administrator'),
        ('teacher', 'Teacher'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    user_type = models.CharField(max_length=15, choices=USER_TYPES, default='teacher')
    phone_number = models.CharField(max_length=20, blank=True)
    employee_id = models.CharField(max_length=20, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.get_user_type_display()})"

    @property
    def is_system_admin(self):
        return self.user_type == 'system_admin' or self.user.is_superuser

    @property
    def is_school_admin(self):
        return self.user_type == 'school_admin'

    @property
    def is_teacher(self):
        return self.user_type == 'teacher'

    @property
    def is_site_admin(self):
        return self.user.is_superuser

class School(models.Model):
    """School profile and configuration"""
    SETUP_STEPS = [
        ('profile', 'School Profile Setup'),
        ('grading', 'Grading Configuration'),
        ('classes', 'Class & Subject Setup'),
        ('students', 'Student Enrollment'),
        ('grading_fields', 'Grading Fields Setup'),
        ('terms', 'Term Setup'),
        ('complete', 'Setup Complete'),
    ]

    TERM_CHOICES = [
        ('FIRST TERM', 'First Term'),
        ('SECOND TERM', 'Second Term'),
        ('THIRD TERM', 'Third Term'),
    ]

    # Basic Information
    admin = models.OneToOneField(User, on_delete=models.CASCADE, related_name='school')
    name = models.CharField(max_length=200)
    motto = models.CharField(max_length=300, blank=True)
    logo = models.ImageField(upload_to='school_logos/', blank=True, null=True)
    phone_number = models.CharField(max_length=20)
    address = models.TextField()
    location = models.CharField(max_length=200, blank=True)  # City, Region

    # Academic Configuration
    current_term = models.CharField(max_length=20, choices=TERM_CHOICES, default='FIRST TERM')
    academic_year = models.CharField(max_length=20)
    next_term_reopening_date = models.DateField(null=True, blank=True, help_text="Date when school reopens for next term")

    # Setup Progress
    setup_step = models.CharField(max_length=20, choices=SETUP_STEPS, default='profile')
    setup_completed = models.BooleanField(default=False)
    setup_completed_at = models.DateTimeField(null=True, blank=True)

    # Report Template
    report_template = models.ForeignKey(
        'SystemReportCardTemplate',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='schools'
    )
    custom_report_config = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def get_setup_progress(self):
        """Calculate setup completion percentage"""
        current_step_index = next(
            (i for i, (step, _) in enumerate(self.SETUP_STEPS) if step == self.setup_step),
            0
        )
        return (current_step_index / len(self.SETUP_STEPS)) * 100

    def advance_setup_step(self):
        """Move to next setup step"""
        current_index = next(
            (i for i, (step, _) in enumerate(self.SETUP_STEPS) if step == self.setup_step),
            0
        )
        if current_index < len(self.SETUP_STEPS) - 1:
            self.setup_step = self.SETUP_STEPS[current_index + 1][0]
            if self.setup_step == 'complete':
                self.setup_completed = True
                self.setup_completed_at = timezone.now()
            self.save()

    def get_grading_scale_json(self):
        """Get grading scale as JSON for JavaScript use"""
        import json
        try:
            # Get school settings to determine grading system
            settings = getattr(self, 'settings', None)
            if not settings:
                settings = SchoolSettings.get_for_school(self)

            grading_system = settings.grading_system

            # Define grading scales based on system
            if grading_system == 'ges_bece':
                # GES BECE system (1-9)
                scale = [
                    {'min': 80, 'max': 100, 'grade': '1'},
                    {'min': 70, 'max': 79, 'grade': '2'},
                    {'min': 65, 'max': 69, 'grade': '3'},
                    {'min': 60, 'max': 64, 'grade': '4'},
                    {'min': 55, 'max': 59, 'grade': '5'},
                    {'min': 50, 'max': 54, 'grade': '6'},
                    {'min': 45, 'max': 49, 'grade': '7'},
                    {'min': 40, 'max': 44, 'grade': '8'},
                    {'min': 0, 'max': 39, 'grade': '9'}
                ]
            elif grading_system == 'alphabetic':
                # Alphabetic system (A-F)
                scale = [
                    {'min': 90, 'max': 100, 'grade': 'A'},
                    {'min': 80, 'max': 89, 'grade': 'B'},
                    {'min': 70, 'max': 79, 'grade': 'C'},
                    {'min': 60, 'max': 69, 'grade': 'D'},
                    {'min': 0, 'max': 59, 'grade': 'F'}
                ]
            elif grading_system == 'numeric':
                # Numeric system (1-5)
                scale = [
                    {'min': 80, 'max': 100, 'grade': '1'},
                    {'min': 70, 'max': 79, 'grade': '2'},
                    {'min': 60, 'max': 69, 'grade': '3'},
                    {'min': 50, 'max': 59, 'grade': '4'},
                    {'min': 0, 'max': 49, 'grade': '5'}
                ]
            else:
                # Default WAEC system (A1-F9)
                scale = [
                    {'min': 80, 'max': 100, 'grade': 'A1'},
                    {'min': 70, 'max': 79, 'grade': 'B2'},
                    {'min': 65, 'max': 69, 'grade': 'B3'},
                    {'min': 60, 'max': 64, 'grade': 'C4'},
                    {'min': 55, 'max': 59, 'grade': 'C5'},
                    {'min': 50, 'max': 54, 'grade': 'C6'},
                    {'min': 45, 'max': 49, 'grade': 'D7'},
                    {'min': 40, 'max': 44, 'grade': 'E8'},
                    {'min': 0, 'max': 39, 'grade': 'F9'}
                ]

            return json.dumps(scale)
        except Exception:
            # Fallback to WAEC system
            return json.dumps([
                {'min': 80, 'max': 100, 'grade': 'A1'},
                {'min': 70, 'max': 79, 'grade': 'B2'},
                {'min': 65, 'max': 69, 'grade': 'B3'},
                {'min': 60, 'max': 64, 'grade': 'C4'},
                {'min': 55, 'max': 59, 'grade': 'C5'},
                {'min': 50, 'max': 54, 'grade': 'C6'},
                {'min': 45, 'max': 49, 'grade': 'D7'},
                {'min': 40, 'max': 44, 'grade': 'E8'},
                {'min': 0, 'max': 39, 'grade': 'F9'}
            ])


class SiteSettings(models.Model):
    """Site-wide settings managed by site admin only"""

    # Email Configuration (SMTP)
    email_host = models.CharField(max_length=255, default='smtp.gmail.com', help_text="SMTP server address")
    email_port = models.IntegerField(default=587, help_text="SMTP port (usually 587 for TLS or 465 for SSL)")
    email_host_user = models.EmailField(help_text="Email address for sending emails")
    email_host_password = models.CharField(max_length=255, help_text="Email password or app password")
    email_use_tls = models.BooleanField(default=True, help_text="Use TLS encryption")
    email_use_ssl = models.BooleanField(default=False, help_text="Use SSL encryption")

    # Site Information
    site_name = models.CharField(max_length=200, default="Smart Terminal Report System")
    support_email = models.EmailField(blank=True, help_text="Support contact email")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Site Settings"
        verbose_name_plural = "Site Settings"

    def __str__(self):
        return f"Site Settings - {self.site_name}"

    @classmethod
    def get_settings(cls):
        """Get or create site settings"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class SchoolSettings(models.Model):
    """Model to store school admin settings and configurations (no SMTP)"""
    school = models.OneToOneField(School, on_delete=models.CASCADE, related_name='settings')

    # Notification Settings
    send_welcome_emails = models.BooleanField(default=True)
    send_password_reset_emails = models.BooleanField(default=True)
    send_grade_notifications = models.BooleanField(default=False)

    # Report Settings
    default_report_template = models.CharField(max_length=100, default='standard')
    include_school_logo_in_reports = models.BooleanField(default=True)
    show_class_position = models.BooleanField(default=True)
    show_overall_position = models.BooleanField(default=True)

    # Academic Settings
    grading_system = models.CharField(
        max_length=20,
        choices=[
            ('waec', 'WAEC (A1-F9) - For SHS'),
            ('ges_bece', 'GES BECE (1-9) - For Basic Schools'),
            ('alphabetic', 'Alphabetic (A-F)'),
            ('numeric', 'Numeric (1-5)'),
            ('percentage', 'Percentage'),
        ],
        default='waec'
    )

    # System Settings
    auto_calculate_positions = models.BooleanField(default=True)
    allow_score_editing_after_submission = models.BooleanField(default=False)
    require_teacher_comments = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "School Settings"
        verbose_name_plural = "School Settings"

    def __str__(self):
        return f"Settings for {self.school.name}"

    @classmethod
    def get_for_school(cls, school):
        """Get or create settings for a school"""
        settings, created = cls.objects.get_or_create(school=school)
        return settings


class SchoolGradingField(models.Model):
    """School-specific grading fields configuration"""
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='grading_fields')
    system_field = models.ForeignKey(
        SystemGradingField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Reference to system-defined field"
    )
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=20)
    percentage = models.IntegerField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    max_score = models.IntegerField(default=100)
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'name']
        unique_together = ['school', 'code']

    def __str__(self):
        return f"{self.school.name} - {self.name} ({self.percentage}%)"



class Class(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='classes')
    name = models.CharField(max_length=50)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name_plural = "Classes"
        unique_together = ['school', 'name']
    
    def __str__(self):
        return f"{self.school.name} - {self.name}"

class Subject(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='subjects')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['school', 'name']
    
    def __str__(self):
        return self.name

class TeacherAssignment(models.Model):
    """Assigns teachers to specific classes and subjects"""
    teacher = models.ForeignKey(User, on_delete=models.CASCADE, related_name='teacher_assignments')
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='teacher_assignments')
    class_assigned = models.ForeignKey(Class, on_delete=models.CASCADE, related_name='teacher_assignments')
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='teacher_assignments')
    is_class_teacher = models.BooleanField(default=False)  # Main class teacher
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['teacher', 'class_assigned', 'subject']

    def __str__(self):
        role = "Class Teacher" if self.is_class_teacher else "Subject Teacher"
        return f"{self.teacher.get_full_name()} - {self.class_assigned.name} - {self.subject.name} ({role})"

    @classmethod
    def assign_class_teacher(cls, teacher, school, class_obj):
        """
        Assign a teacher as class teacher and automatically assign them to all subjects
        """
        # Remove class teacher status from others in this class
        cls.objects.filter(
            school=school,
            class_assigned=class_obj,
            is_class_teacher=True
        ).update(is_class_teacher=False)

        # Get all subjects for this school
        subjects = Subject.objects.filter(school=school, is_active=True)

        assignments_created = []
        assignments_updated = []

        for subject in subjects:
            assignment, created = cls.objects.get_or_create(
                teacher=teacher,
                school=school,
                class_assigned=class_obj,
                subject=subject,
                defaults={'is_class_teacher': True}
            )

            if created:
                assignments_created.append(subject.name)
            else:
                # Update existing assignment to make them class teacher
                assignment.is_class_teacher = True
                assignment.save()
                assignments_updated.append(subject.name)

        return assignments_created, assignments_updated

class ClassSubject(models.Model):
    """Links subjects to classes - simplified"""
    class_name = models.ForeignKey(Class, on_delete=models.CASCADE, related_name='class_subjects')
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['class_name', 'subject']

    def __str__(self):
        return f"{self.class_name.name} - {self.subject.name}"

    def get_teacher(self):
        """Get the assigned teacher for this class-subject combination"""
        assignment = TeacherAssignment.objects.filter(
            class_assigned=self.class_name,
            subject=self.subject,
            is_active=True
        ).first()
        return assignment.teacher if assignment else None

class Student(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='students')
    student_id = models.CharField(max_length=20, unique=True, blank=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES)
    parent_phone = models.CharField(max_length=20)
    parent_name = models.CharField(max_length=100, blank=True)
    current_class = models.ForeignKey(Class, on_delete=models.CASCADE, related_name='students')
    profile_picture = models.ImageField(upload_to='student_photos/', blank=True, null=True)
    id_card_image = models.ImageField(upload_to='student_id_cards/', blank=True, null=True, help_text="Upload student ID card image")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['school', 'first_name', 'last_name', 'current_class']

    def save(self, *args, **kwargs):
        # Generate student ID if not provided
        if not self.student_id:
            # Get the next number for this school
            last_student = Student.objects.filter(
                school=self.school,
                student_id__startswith='STU'
            ).order_by('-id').first()

            if last_student and last_student.student_id.startswith('STU'):
                try:
                    # Extract number from last student ID (e.g., STU104 -> 104)
                    last_number = int(last_student.student_id[3:])
                    next_number = last_number + 1
                except (ValueError, IndexError):
                    next_number = 101  # Start from 101 if parsing fails
            else:
                next_number = 101  # Start from 101 for first student

            self.student_id = f'STU{next_number}'

            # Ensure uniqueness (in case of race conditions)
            while Student.objects.filter(student_id=self.student_id).exists():
                next_number += 1
                self.student_id = f'STU{next_number}'

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

class TermConfiguration(models.Model):
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='term_configs')
    term = models.CharField(max_length=15, choices=[
        ('First Term', 'First Term'),
        ('Second Term', 'Second Term'),
        ('Third Term', 'Third Term'),
    ])
    academic_year = models.CharField(max_length=20)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    next_term_reopening_date = models.DateField(null=True, blank=True, help_text="Date when school reopens for next term")
    classwork_percentage = models.IntegerField(default=30, validators=[MinValueValidator(0), MaxValueValidator(100)])
    test_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    exam_percentage = models.IntegerField(default=70, validators=[MinValueValidator(0), MaxValueValidator(100)])
    max_classwork_score = models.IntegerField(default=100)
    max_test_score = models.IntegerField(default=100)
    max_exam_score = models.IntegerField(default=100)
    comments_required = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    
    class Meta:
        unique_together = ['school', 'term', 'academic_year']
    
    def __str__(self):
        return f"{self.school.name} - {self.term} {self.academic_year}"

class Score(models.Model):
    """Flexible score model that adapts to school's grading fields"""
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='scores')
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE)
    teacher = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scores_entered')
    term = models.CharField(max_length=20)
    academic_year = models.CharField(max_length=20)

    # Flexible score storage - JSON field to store all grading field scores
    scores_data = models.JSONField(default=dict, help_text="Stores scores for each grading field")

    # Calculated fields
    total_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    grade = models.CharField(max_length=5, blank=True)
    position = models.IntegerField(default=0, help_text="Position in class for this subject")

    # Tracking
    is_draft = models.BooleanField(default=True)
    is_submitted = models.BooleanField(default=False)
    submitted_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['student', 'subject', 'term', 'academic_year']
        ordering = ['-total_score']

    def __str__(self):
        return f"{self.student.full_name} - {self.subject.name} - {self.term}"

    def get_score_for_field(self, field_code):
        """Get score for a specific grading field"""
        return self.scores_data.get(field_code, 0)

    def set_score_for_field(self, field_code, score):
        """Set score for a specific grading field"""
        self.scores_data[field_code] = float(score)
        self.save()

    def calculate_total(self):
        """Calculate total score based on school's grading fields configuration"""
        school = self.student.school
        grading_fields = school.grading_fields.filter(is_active=True)

        if not grading_fields.exists():
            # Create default fields if none exist
            SchoolGradingField.create_default_fields(school)
            grading_fields = school.grading_fields.filter(is_active=True)

        total_weighted_score = 0
        total_percentage = 0

        # Get entry mode from scores_data (if available)
        entry_mode = self.scores_data.get('_entry_mode', 'raw_scores')

        # Calculate weighted total based on grading fields and entry mode
        for field in grading_fields:
            field_score = self.get_score_for_field(field.code)

            if entry_mode == 'raw_scores':
                # Mode 1: Raw scores - calculate everything normally
                normalized_score = (field_score / float(field.max_score)) * 100 if field.max_score > 0 else 0
                weighted_score = (normalized_score * float(field.percentage)) / 100
            elif entry_mode == 'converted_classwork':
                # Mode 2: Classwork already converted, exam needs calculation
                if field.code in ['CW', 'classwork']:
                    # Classwork is already converted to percentage, just use it
                    weighted_score = field_score
                else:
                    # Other fields (like exam) need full calculation
                    normalized_score = (field_score / float(field.max_score)) * 100 if field.max_score > 0 else 0
                    weighted_score = (normalized_score * float(field.percentage)) / 100
            elif entry_mode == 'all_converted':
                # Mode 3: All scores already converted, just add them
                weighted_score = field_score
            else:
                # Default to raw scores mode
                normalized_score = (field_score / float(field.max_score)) * 100 if field.max_score > 0 else 0
                weighted_score = (normalized_score * float(field.percentage)) / 100

            total_weighted_score += weighted_score
            total_percentage += float(field.percentage)

        # Final score (should be out of 100 if percentages add up to 100)
        self.total_score = total_weighted_score

        # Calculate grade using school's configured grading scale
        try:
            grading_scale = school.grading_scale
            grade_ranges = grading_scale.grades.all().order_by('-min_score')  # Highest first

            # Find the appropriate grade range
            for grade_range in grade_ranges:
                if grade_range.min_score <= self.total_score <= grade_range.max_score:
                    self.grade = grade_range.grade
                    self.remark = grade_range.remark.upper()
                    break
            else:
                # If no range found, use default based on grading type
                if grading_scale.grading_type == 'numeric':
                    self.grade = '5' if self.total_score < 40 else '4'
                    self.remark = 'FAIL' if self.total_score < 40 else 'PASS'
                elif grading_scale.grading_type == 'alphabetic':
                    self.grade = 'F' if self.total_score < 40 else 'D'
                    self.remark = 'FAIL' if self.total_score < 40 else 'PASS'
                else:  # waec or default
                    self.grade = 'F9' if self.total_score < 40 else 'E8'
                    self.remark = 'FAIL' if self.total_score < 40 else 'PASS'

        except Exception as e:
            # Fallback to WAEC system if grading scale not configured
            if self.total_score >= 80:
                self.grade = 'A1'
                self.remark = 'EXCELLENT'
            elif self.total_score >= 70:
                self.grade = 'B2'
                self.remark = 'VERY GOOD'
            elif self.total_score >= 65:
                self.grade = 'B3'
                self.remark = 'GOOD'
            elif self.total_score >= 60:
                self.grade = 'C4'
                self.remark = 'CREDIT'
            elif self.total_score >= 55:
                self.grade = 'C5'
                self.remark = 'CREDIT'
            elif self.total_score >= 50:
                self.grade = 'C6'
                self.remark = 'CREDIT'
            elif self.total_score >= 45:
                self.grade = 'D7'
                self.remark = 'PASS'
            elif self.total_score >= 40:
                self.grade = 'E8'
                self.remark = 'PASS'
            else:
                self.grade = 'F9'
                self.remark = 'FAIL'

        self.save()
        return self.total_score

    def calculate_position(self):
        """Calculate position in class for this subject"""
        # Get all scores for this subject, class, term, and academic year
        class_scores = Score.objects.filter(
            subject=self.subject,
            student__current_class=self.student.current_class,
            term=self.term,
            academic_year=self.academic_year,
            is_submitted=True
        ).order_by('-total_score')

        # Find position
        for index, score in enumerate(class_scores, 1):
            if score.id == self.id:
                self.position = index
                self.save()
                break

        return self.position

    # Properties for template access to individual score components
    @property
    def classwork_score(self):
        """Get classwork score for template display"""
        return self.get_score_for_field('CW') or self.get_score_for_field('classwork') or 0

    @property
    def test_score(self):
        """Get test score for template display"""
        return self.get_score_for_field('TEST') or self.get_score_for_field('test') or 0

    @property
    def exam_score(self):
        """Get exam score for template display"""
        return self.get_score_for_field('EX') or self.get_score_for_field('exam') or 0

    @property
    def project_score(self):
        """Get project score for template display"""
        return self.get_score_for_field('PROJECT') or self.get_score_for_field('project') or 0

    @property
    def assignment_score(self):
        """Get assignment score for template display"""
        return self.get_score_for_field('ASSIGNMENT') or self.get_score_for_field('assignment') or 0

class StudentTermResult(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='term_results')
    term = models.CharField(max_length=10)
    academic_year = models.CharField(max_length=20)
    total_score = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    average_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    position = models.IntegerField(default=0)
    subjects_count = models.IntegerField(default=0)
    teacher_comment = models.TextField(blank=True, help_text="Class teacher's comment")
    headmaster_comment = models.TextField(blank=True)

    # Attendance fields for class teachers
    attendance_present = models.IntegerField(default=0, help_text="Days present")
    attendance_total = models.IntegerField(default=0, help_text="Total school days")

    # Comment fields for class teachers
    conduct_comment = models.CharField(max_length=200, blank=True, help_text="Student's conduct")
    attitude_comment = models.CharField(max_length=200, blank=True, help_text="Student's attitude")
    interest_comment = models.CharField(max_length=200, blank=True, help_text="Student's interest")

    # Promotion information
    promoted_to = models.CharField(max_length=50, blank=True, help_text="Next class/level")

    is_approved = models.BooleanField(default=False)
    sms_sent = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['student', 'term', 'academic_year']
    
    def __str__(self):
        return f"{self.student.full_name} - {self.term} {self.academic_year}"

    @property
    def attendance_percentage(self):
        """Calculate attendance percentage"""
        if self.attendance_total > 0:
            return round((self.attendance_present / self.attendance_total) * 100, 1)
        return 0

    def calculate_position(self):
        """Calculate position in class based on average score"""
        # Get all term results for this class, term, and academic year
        class_results = StudentTermResult.objects.filter(
            student__current_class=self.student.current_class,
            term=self.term,
            academic_year=self.academic_year
        ).order_by('-average_score', '-total_score')

        # Find position
        for index, result in enumerate(class_results, 1):
            if result.id == self.id:
                self.position = index
                self.save()
                break

        return self.position


# System-wide Templates (Managed by System Admin)
class SystemClassTemplate(models.Model):
    """Pre-configured class templates that schools can use"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, choices=[
        ('primary', 'Primary School'),
        ('jhs', 'Junior High School'),
        ('shs', 'Senior High School'),
        ('other', 'Other'),
    ], default='primary')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"


class SystemSubjectTemplate(models.Model):
    """Pre-configured subject templates that schools can use"""
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, choices=[
        ('core', 'Core Subject'),
        ('elective', 'Elective Subject'),
        ('vocational', 'Vocational Subject'),
        ('other', 'Other'),
    ], default='core')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class SystemGradingTemplate(models.Model):
    """Pre-configured grading systems that schools can use"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    grading_type = models.CharField(max_length=20, choices=[
        ('waec', 'WAEC Style (A1-F9)'),
        ('alphabetic', 'Alphabetic (A-F)'),
        ('numeric', 'Numeric (1-5)'),
        ('percentage', 'Percentage (0-100)'),
        ('custom', 'Custom'),
    ], default='waec')
    grade_boundaries = models.JSONField(default=dict, help_text="Grade boundaries and remarks")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_grading_type_display()})"


class SystemReportCardTemplate(models.Model):
    """Pre-configured report card templates that schools can use"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    template_type = models.CharField(max_length=20, choices=[
        ('primary', 'Primary School'),
        ('jhs', 'Junior High School'),
        ('shs', 'Senior High School'),
        ('custom', 'Custom'),
    ], default='primary')
    design_style = models.CharField(max_length=30, choices=[
        ('classic', 'Classic Professional'),
        ('modern', 'Modern Minimalist'),
        ('colorful', 'Colorful & Vibrant'),
        ('elegant', 'Elegant Traditional'),
        ('creative', 'Creative & Artistic'),
    ], default='classic')
    color_scheme = models.CharField(max_length=20, choices=[
        ('blue', 'Professional Blue'),
        ('green', 'Academic Green'),
        ('purple', 'Royal Purple'),
        ('red', 'Vibrant Red'),
        ('orange', 'Energetic Orange'),
        ('teal', 'Modern Teal'),
        ('gray', 'Sophisticated Gray'),
    ], default='blue')
    layout_config = models.JSONField(default=dict, help_text="Layout configuration and styling")
    fields_config = models.JSONField(default=dict, help_text="Fields to include in report card")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['template_type', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def get_features_list(self):
        """Return a list of features for this template"""
        features = []

        # Add features based on design style
        if self.design_style == 'classic':
            features.extend(['Professional layout', 'Traditional design', 'Clean typography'])
        elif self.design_style == 'modern':
            features.extend(['Modern layout', 'Minimalist design', 'Contemporary styling'])
        elif self.design_style == 'colorful':
            features.extend(['Vibrant colors', 'Creative design', 'Engaging appearance'])
        elif self.design_style == 'elegant':
            features.extend(['Elegant design', 'Sophisticated layout', 'Premium appearance'])
        elif self.design_style == 'creative':
            features.extend(['Creative design', 'Artistic elements', 'Unique styling'])

        # Add features based on color scheme
        if self.color_scheme == 'blue':
            features.append('Professional blue theme')
        elif self.color_scheme == 'green':
            features.append('Academic green theme')
        elif self.color_scheme == 'orange':
            features.append('Energetic orange theme')
        elif self.color_scheme == 'purple':
            features.append('Royal purple theme')
        elif self.color_scheme == 'teal':
            features.append('Modern teal theme')
        elif self.color_scheme == 'gray':
            features.append('Sophisticated gray theme')

        # Add template type specific features
        if self.template_type == 'primary':
            features.append('Suitable for primary schools')
        elif self.template_type == 'jhs':
            features.append('Suitable for junior high schools')
        elif self.template_type == 'shs':
            features.append('Suitable for senior high schools')

        return features


class SystemTerm(models.Model):
    """System-wide term configuration"""
    name = models.CharField(max_length=50, unique=True)
    short_name = models.CharField(max_length=10)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class SchoolGradingScale(models.Model):
    """School-specific grading scale configuration"""
    GRADING_TYPES = [
        ('waec', 'WAEC-style (A1, B2, B3, etc.) - For SHS'),
        ('ges_bece', 'GES BECE (1-9) - For Basic Schools'),
        ('alphabetic', 'Alphabetic (A, B, C, D, F)'),
        ('numeric', 'Numeric (1-5)'),
        ('custom', 'Custom Grading Scale'),
    ]

    school = models.OneToOneField(School, on_delete=models.CASCADE, related_name='grading_scale')
    grading_type = models.CharField(max_length=20, choices=GRADING_TYPES, default='waec')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.school.name} - {self.get_grading_type_display()}"

    @classmethod
    def create_default_scale(cls, school, grading_type='waec'):
        """Create default grading scale for a school"""
        scale, created = cls.objects.get_or_create(
            school=school,
            defaults={'grading_type': grading_type}
        )

        if created:
            # Create default grade ranges based on type
            if grading_type == 'waec':
                default_grades = [
                    ('A1', 80, 100, 'Excellent'),
                    ('B2', 70, 79, 'Very Good'),
                    ('B3', 65, 69, 'Good'),
                    ('C4', 60, 64, 'Credit'),
                    ('C5', 55, 59, 'Credit'),
                    ('C6', 50, 54, 'Credit'),
                    ('D7', 45, 49, 'Pass'),
                    ('E8', 40, 44, 'Pass'),
                    ('F9', 0, 39, 'Fail'),
                ]
            elif grading_type == 'alphabetic':
                default_grades = [
                    ('A', 90, 100, 'Excellent'),
                    ('B', 80, 89, 'Very Good'),
                    ('C', 70, 79, 'Good'),
                    ('D', 60, 69, 'Satisfactory'),
                    ('F', 0, 59, 'Fail'),
                ]
            elif grading_type == 'ges_bece':
                default_grades = [
                    ('1', 80, 100, 'Excellent'),
                    ('2', 70, 79, 'Very Good'),
                    ('3', 65, 69, 'Good'),
                    ('4', 60, 64, 'Credit'),
                    ('5', 55, 59, 'Credit'),
                    ('6', 50, 54, 'Credit'),
                    ('7', 45, 49, 'Pass'),
                    ('8', 40, 44, 'Pass'),
                    ('9', 0, 39, 'Fail'),
                ]
            elif grading_type == 'numeric':
                default_grades = [
                    ('1', 80, 100, 'Excellent'),
                    ('2', 70, 79, 'Very Good'),
                    ('3', 60, 69, 'Good'),
                    ('4', 50, 59, 'Satisfactory'),
                    ('5', 0, 49, 'Needs Improvement'),
                ]
            else:  # custom
                default_grades = [
                    ('A', 80, 100, 'Excellent'),
                    ('B', 70, 79, 'Very Good'),
                    ('C', 60, 69, 'Good'),
                    ('D', 50, 59, 'Satisfactory'),
                    ('F', 0, 49, 'Fail'),
                ]

            for order, (grade, min_score, max_score, remark) in enumerate(default_grades):
                SchoolGradeRange.objects.create(
                    grading_scale=scale,
                    grade=grade,
                    min_score=min_score,
                    max_score=max_score,
                    remark=remark,
                    order=order
                )

        return scale


class SchoolGradeRange(models.Model):
    """Individual grade ranges within a school's grading scale"""
    grading_scale = models.ForeignKey(SchoolGradingScale, on_delete=models.CASCADE, related_name='grades')
    grade = models.CharField(max_length=10)  # A1, B, 1, etc.
    min_score = models.DecimalField(max_digits=5, decimal_places=2)
    max_score = models.DecimalField(max_digits=5, decimal_places=2)
    remark = models.CharField(max_length=100)  # Excellent, Good, Fair, etc.
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']
        unique_together = ['grading_scale', 'grade']

    def __str__(self):
        return f"{self.grade}: {self.min_score}-{self.max_score} ({self.remark})"


class SchoolGradingField(models.Model):
    """Configurable grading fields for schools (e.g., Classwork, Test, Exam)"""
    school = models.ForeignKey(School, on_delete=models.CASCADE, related_name='grading_fields')
    name = models.CharField(max_length=50)  # Classwork, Test, Exam, Project, etc.
    code = models.CharField(max_length=20)  # classwork, test, exam, project, etc.
    percentage = models.DecimalField(max_digits=5, decimal_places=2)  # Percentage weight
    max_score = models.DecimalField(max_digits=5, decimal_places=2, default=100)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order']
        unique_together = ['school', 'code']

    def __str__(self):
        return f"{self.school.name} - {self.name} ({self.percentage}%)"

    @classmethod
    def create_default_fields(cls, school):
        """Create default grading fields for a school"""
        default_fields = [
            ('Classwork', 'CW', 30, 100, 0),
            ('Test', 'TEST', 0, 100, 1),
            ('Exam', 'EX', 70, 100, 2),
        ]

        for name, code, percentage, max_score, order in default_fields:
            cls.objects.get_or_create(
                school=school,
                code=code,
                defaults={
                    'name': name,
                    'percentage': percentage,
                    'max_score': max_score,
                    'order': order
                }
            )
