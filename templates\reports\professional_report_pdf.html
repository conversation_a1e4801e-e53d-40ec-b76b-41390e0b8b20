<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ student.full_name }} - Terminal Report</title>
    <style>
        @page {
            size: A4;
            margin: 12mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.2;
            color: #000;
            background: white;
        }

        .report-container {
            width: 100%;
            margin: 0 auto;
            border: 3px solid #000;
            padding: 8px;
            background: white;
        }

        /* Header Section */
        .header-section {
            border: 3px solid #2E5BBA;
            margin-bottom: 8px;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
        }

        .header-table td {
            border: 1px solid #2E5BBA;
            padding: 10px;
            vertical-align: middle;
        }

        .logo-cell {
            width: 95px;
            text-align: center;
            background: #f8f9ff;
            padding: 5px;
        }

        .school-logo-img {
            width: 80px;
            height: 80px;
            object-fit: contain;
            border-radius: 50%;
            border: 2px solid #2E5BBA;
        }

        .school-logo-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #2E5BBA;
            color: white;
            font-weight: bold;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin: 0 auto;
        }

        .school-info-cell {
            text-align: center;
            background: white;
        }

        .photo-cell {
            width: 95px;
            text-align: center;
            background: #f8f9ff;
            padding: 5px;
        }

        .school-name {
            font-size: 18px;
            font-weight: bold;
            color: #2E5BBA;
            margin-bottom: 3px;
            text-transform: uppercase;
        }

        .school-complex {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 3px;
            color: #000;
        }

        .school-details {
            font-size: 9px;
            line-height: 1.4;
            color: #333;
        }

        /* Terminal Report Header */
        .terminal-header {
            background-color: #2E5BBA;
            color: white;
            text-align: center;
            padding: 8px;
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 8px;
            text-transform: uppercase;
        }
        
        /* Student Information Table */
        .student-info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
            border: 2px solid #2E5BBA;
        }

        .student-info-table td {
            border: 1px solid #2E5BBA;
            padding: 6px 8px;
            font-size: 10px;
        }

        .student-info-table .info-label {
            font-weight: bold;
            background-color: #f8f9ff;
            width: 20%;
            color: #000;
        }

        .student-info-table .info-value {
            background-color: white;
            width: 30%;
            color: #000;
            text-transform: uppercase;
        }

        /* Scores Table */
        .scores-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
            font-size: 11px;
            table-layout: fixed;
        }

        .scores-table th,
        .scores-table td {
            border: 2px solid #2E5BBA;
            padding: 8px 4px;
            text-align: center;
            vertical-align: middle;
        }

        .scores-table th {
            background-color: #2E5BBA;
            color: white;
            font-weight: bold;
            font-size: 10px;
            line-height: 1.2;
        }

        .scores-table .subject-name {
            text-align: left;
            font-weight: bold;
            padding-left: 8px;
            font-size: 10px;
            width: 25%;
        }

        .scores-table .total-score {
            font-weight: bold;
            color: #2E5BBA;
            font-size: 11px;
        }

        /* Score cells styling */
        .score-cell {
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            width: 12%;
        }

        /* Position cell styling */
        .position-cell {
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            width: 8%;
        }

        /* Remarks column styling */
        .remarks-cell {
            text-align: left;
            padding-left: 8px;
            font-size: 9px;
            font-weight: bold;
            width: 22%;
        }

        /* Grade cell styling */
        .grade-cell {
            text-align: center;
            font-weight: bold;
            font-size: 11px;
            width: 10%;
        }

        /* Summary Section */
        .summary-section {
            margin-bottom: 8px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 4px;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .summary-box {
            border: 2px solid #2E5BBA;
            padding: 8px;
            text-align: center;
            background: #f8f9ff;
            width: 33.33%;
        }

        .summary-box .title {
            font-weight: bold;
            font-size: 9px;
            margin-bottom: 4px;
            color: #000;
        }

        .summary-box .value {
            font-size: 12px;
            font-weight: bold;
            color: #2E5BBA;
        }

        /* Comments Section */
        .comments-section {
            margin-bottom: 8px;
        }

        .comments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .comment-box {
            border: 2px solid #2E5BBA;
            padding: 8px;
            width: 50%;
            vertical-align: top;
            min-height: 40px;
        }

        .comment-box .title {
            font-weight: bold;
            font-size: 9px;
            margin-bottom: 4px;
            color: #000;
        }

        .comment-box .content {
            font-size: 9px;
            color: #333;
            line-height: 1.3;
        }

        /* Signature Section */
        .signature-section {
            margin-top: 8px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signature-box {
            text-align: left;
            width: 50%;
            padding: 8px;
            border: 2px solid #2E5BBA;
            vertical-align: bottom;
        }

        .signature-line {
            border-bottom: 2px solid #000;
            height: 25px;
            margin-bottom: 6px;
        }

        .signature-title {
            font-weight: bold;
            font-size: 10px;
            color: #000;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Header Section -->
        <div class="header-section">
            <table class="header-table">
                <tr>
                    <!-- School Logo -->
                    <td class="logo-cell">
                        {% if school_logo_base64 %}
                            <img src="data:image/png;base64,{{ school_logo_base64 }}" alt="{{ school.name }}" class="school-logo-img">
                        {% elif school.logo %}
                            <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="school-logo-img">
                        {% else %}
                            <div class="school-logo-placeholder">
                                SCHOOL<br>LOGO
                            </div>
                        {% endif %}
                    </td>

                    <!-- School Information -->
                    <td class="school-info-cell">
                        <div class="school-name">{{ school.name|upper }}</div>
                        <div class="school-complex">SCHOOL COMPLEX</div>
                        <div class="school-details">
                            {% if school.address %}{{ school.address }}{% else %}School Address{% endif %}<br>
                            {% if school.phone_number %}{{ school.phone_number }}{% else %}School Phone{% endif %}<br>
                            {% if school.admin.email %}{{ school.admin.email }}{% else %}<EMAIL>{% endif %}
                        </div>
                    </td>

                    <!-- Student Photo -->
                    <td class="photo-cell">
                        {% if student_photo_base64 %}
                            <img src="data:image/jpeg;base64,{{ student_photo_base64 }}" alt="{{ student.full_name }}" style="width: 85px; height: 100px; object-fit: cover; border: 2px solid #2E5BBA;">
                        {% else %}
                            <div class="photo-placeholder">
                                <div style="width: 85px; height: 100px; border: 2px solid #2E5BBA; background-color: #f0f8ff; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 10px; text-align: center; color: #2E5BBA;">
                                    STUDENT<br>PHOTO
                                </div>
                            </div>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>

        <!-- Terminal Report Header -->
        <div class="terminal-header">
            TERMINAL REPORT SHEET
        </div>

        <!-- Student Information -->
        <table class="student-info-table">
            <tr>
                <td class="info-label">Student ID:</td>
                <td class="info-value">{{ student.student_id|default:"N/A" }}</td>
                <td class="info-label">Academic Year:</td>
                <td class="info-value">{{ academic_year|default:"N/A" }}</td>
            </tr>
            <tr>
                <td class="info-label">Name:</td>
                <td class="info-value">{{ student.full_name|upper }}</td>
                <td class="info-label">Academic Term:</td>
                <td class="info-value">{{ current_term|default:"N/A" }}</td>
            </tr>
            <tr>
                <td class="info-label">Class:</td>
                <td class="info-value">{{ student.current_class.name|default:"N/A" }}</td>
                <td class="info-label">School reopens:</td>
                <td class="info-value">
                    {% if school.next_term_reopening_date %}
                        {{ school.next_term_reopening_date|date:"F d, y"|upper }}
                    {% else %}
                        TBA
                    {% endif %}
                </td>
            </tr>
        </table>

        <!-- Academic Performance Table -->
        <table class="scores-table">
            <thead>
                <tr>
                    <th style="width: 25%;">SUBJECT</th>
                    {% for field in grading_fields %}
                    <th style="width: 12%; background-color: #2E5BBA; color: white;">{{ field.name|upper }}<br>({{ field.percentage|floatformat:0 }}%)</th>
                    {% empty %}
                    <th style="width: 12%; background-color: #2E5BBA; color: white;">CLASSWORK<br>(30%)</th>
                    <th style="width: 13%; background-color: #2E5BBA; color: white;">EXAM<br>(70%)</th>
                    {% endfor %}
                    <th style="width: 12%;">TOTAL<br>SCORE</th>
                    <th style="width: 10%;">GRADE</th>
                    <th style="width: 8%;">POS.</th>
                    <th style="width: 22%;">REMARKS</th>
                </tr>
            </thead>
            <tbody>
                {% for subject_info in subject_data %}
                <tr>
                    <td class="subject-name">{{ subject_info.subject.name }}</td>
                    {% for field in grading_fields %}
                    <td class="score-cell">
                        {% if subject_info.has_data and subject_info.score %}
                            {% load score_filters %}
                            {% with display_score=subject_info.score|get_display_score:field.code %}
                                {% if display_score %}
                                    {{ display_score|floatformat:0 }}
                                {% else %}
                                    -
                                {% endif %}
                            {% endwith %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    {% empty %}
                    <td class="score-cell">{{ subject_info.classwork_score|default:"-" }}</td>
                    <td class="score-cell">{{ subject_info.exam_score|default:"-" }}</td>
                    {% endfor %}
                    <td class="total-score">
                        {% if subject_info.has_data %}
                            {{ subject_info.total_score|floatformat:0 }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="grade-cell">
                        {% if subject_info.has_data %}
                            {{ subject_info.grade }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="position-cell">
                        {% if subject_info.has_data and subject_info.position > 0 %}
                            {{ subject_info.position }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="remarks-cell">
                        {% if subject_info.has_data %}
                            {% if subject_info.grade == 'A' or subject_info.grade == 'A1' or subject_info.grade == '1' %}EXCELLENT
                            {% elif subject_info.grade == 'B' or subject_info.grade == 'B2' or subject_info.grade == 'B3' or subject_info.grade == '2' or subject_info.grade == '3' %}VERY GOOD
                            {% elif subject_info.grade == 'C' or subject_info.grade == 'C4' or subject_info.grade == 'C5' or subject_info.grade == 'C6' or subject_info.grade == '4' or subject_info.grade == '5' or subject_info.grade == '6' %}GOOD
                            {% elif subject_info.grade == 'D' or subject_info.grade == 'D7' or subject_info.grade == '7' %}PASS
                            {% else %}WEAK
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                {% for score in scores %}
                <tr>
                    <td class="subject-name">{{ score.subject.name }}</td>
                    <td class="score-cell">{{ score.classwork_score|default:"-" }}</td>
                    <td class="score-cell">{{ score.exam_score|default:"-" }}</td>
                    <td class="total-score">{{ score.total_score|floatformat:0 }}</td>
                    <td class="grade-cell">{{ score.grade|default:"-" }}</td>
                    <td class="position-cell">{{ score.position|default:"-" }}</td>
                    <td class="remarks-cell">
                        {% if score.grade == "A" or score.grade == "A1" or score.grade == "1" %}
                            EXCELLENT
                        {% elif score.grade == "B" or score.grade == "B2" or score.grade == "B3" or score.grade == "2" or score.grade == "3" %}
                            VERY GOOD
                        {% elif score.grade == "C" or score.grade == "C4" or score.grade == "C5" or score.grade == "C6" or score.grade == "4" or score.grade == "5" or score.grade == "6" %}
                            GOOD
                        {% elif score.grade == "D" or score.grade == "D7" or score.grade == "7" %}
                            PASS
                        {% else %}
                            WEAK
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
                {% endfor %}
            </tbody>
        </table>

        <!-- Summary Section -->
        <div class="summary-section">
            <table class="summary-table">
                <tr>
                    <td class="summary-box">
                        <div class="title">No On Roll:</div>
                        <div class="value">{{ total_students|default:"N/A" }}</div>
                    </td>
                    <td class="summary-box">
                        <div class="title">Total Marks:</div>
                        <div class="value">{{ total_score|floatformat:1|default:"0.0" }}</div>
                    </td>
                    <td class="summary-box">
                        <div class="title">Average Mark:</div>
                        <div class="value">{{ average_score|floatformat:1|default:"0.0" }}</div>
                    </td>
                </tr>
                <tr>
                    <td class="summary-box">
                        <div class="title">Attendance:</div>
                        <div class="value">{{ attendance|default:"Not Set" }}</div>
                    </td>
                    <td class="summary-box">
                        <div class="title">Promoted to:</div>
                        <div class="value">{{ promoted_to|default:"Not Set" }}</div>
                    </td>
                    <td class="summary-box">
                        <div class="title">Position in Class:</div>
                        <div class="value">{% if class_position %}{{ class_position }}{% if class_position == 1 %}st{% elif class_position == 2 %}nd{% elif class_position == 3 %}rd{% else %}th{% endif %}{% else %}Not Set{% endif %}</div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Comments Section -->
        <div class="comments-section">
            <table class="comments-table">
                <tr>
                    <td class="comment-box">
                        <div class="title">Conduct:</div>
                        <div class="content">{{ conduct|default:"Respectful and obedient." }}</div>
                    </td>
                    <td class="comment-box">
                        <div class="title">Attitude:</div>
                        <div class="content">{{ attitude|default:"Hardworking" }}</div>
                    </td>
                </tr>
                <tr>
                    <td class="comment-box">
                        <div class="title">Interest:</div>
                        <div class="content">{{ interest|default:"Shows keen interest in studies" }}</div>
                    </td>
                    <td class="comment-box">
                        <div class="title">Class Teacher's Remark:</div>
                        <div class="content">{{ teacher_comment|default:"Good performance. Keep it up." }}</div>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Signature Section -->
        <div class="signature-section">
            <table class="signature-table">
                <tr>
                    <td class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-title">Class Teacher's Signature</div>
                    </td>
                    <td class="signature-box">
                        <div class="signature-line"></div>
                        <div class="signature-title">Headmaster's Signature</div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
