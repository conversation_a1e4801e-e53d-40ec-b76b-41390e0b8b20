{% extends 'base.html' %}

{% block title %}Site Administration Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
    <!-- Mobile-First Header -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
            <div class="flex flex-col space-y-4 py-4 sm:py-6">
                <div class="text-center sm:text-left">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Site Administration</h1>
                    <p class="text-sm sm:text-base text-gray-600">System-wide management and analytics</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <a href="/admin/" target="_blank"
                       class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center active:scale-95">
                        <i data-lucide="settings" class="w-4 h-4 inline mr-2"></i>
                        Django Admin
                    </a>
                    <a href="{% url 'site_admin_system_settings' %}"
                       class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center active:scale-95">
                        <i data-lucide="server" class="w-4 h-4 inline mr-2"></i>
                        System Settings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile-First Main Content -->
    <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <!-- Mobile-Optimized Statistics Cards -->
        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow active:scale-95">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="building" class="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-purple-600"></i>
                    </div>
                    <div class="sm:ml-3 lg:ml-4 text-center sm:text-left">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Schools</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{{ total_schools }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow active:scale-95">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="users" class="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-blue-600"></i>
                    </div>
                    <div class="sm:ml-3 lg:ml-4 text-center sm:text-left">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{{ total_users }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow active:scale-95">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-green-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="graduation-cap" class="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-green-600"></i>
                    </div>
                    <div class="sm:ml-3 lg:ml-4 text-center sm:text-left">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Students</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{{ total_students }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow active:scale-95">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="user-check" class="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-yellow-600"></i>
                    </div>
                    <div class="sm:ml-3 lg:ml-4 text-center sm:text-left">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Teachers</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{{ total_teachers }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow active:scale-95">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-red-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="shield" class="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-red-600"></i>
                    </div>
                    <div class="sm:ml-3 lg:ml-4 text-center sm:text-left">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Admins</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{{ total_school_admins }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile-First Quick Actions -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-6 sm:mb-8">
            <div class="flex items-center justify-between mb-4 sm:mb-6">
                <h3 class="text-lg sm:text-xl font-bold text-gray-900 flex items-center">
                    <i data-lucide="zap" class="w-5 h-5 mr-2 text-purple-600"></i>
                    Quick Actions
                </h3>
                <span class="text-xs sm:text-sm text-gray-500">Site Management</span>
            </div>

            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
                <a href="{% url 'site_admin_schools' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 active:scale-95 group">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                        <i data-lucide="building" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-purple-900 text-center">Schools</span>
                </a>

                <a href="{% url 'site_admin_users' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-200 active:scale-95 group">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                        <i data-lucide="users" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-blue-900 text-center">Users</span>
                </a>

                <a href="{% url 'site_admin_analytics' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl hover:from-green-100 hover:to-green-200 transition-all duration-200 active:scale-95 group">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                        <i data-lucide="bar-chart-3" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-green-900 text-center">Analytics</span>
                </a>

                <a href="{% url 'site_admin_system_settings' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl hover:from-gray-100 hover:to-gray-200 transition-all duration-200 active:scale-95 group">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gray-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                        <i data-lucide="settings" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-900 text-center">Settings</span>
                </a>

                <a href="{% url 'site_admin_report_templates' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl hover:from-orange-100 hover:to-orange-200 transition-all duration-200 active:scale-95 group">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-orange-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                        <i data-lucide="file-text" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-orange-900 text-center">Templates</span>
                </a>

                <a href="/admin/" target="_blank"
                   class="flex flex-col items-center p-3 sm:p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl hover:from-indigo-100 hover:to-indigo-200 transition-all duration-200 active:scale-95 group">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-indigo-600 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform">
                        <i data-lucide="database" class="w-5 h-5 sm:w-6 sm:h-6 text-white"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-indigo-900 text-center">Django</span>
                </a>
            </div>
        </div>

        <!-- Recent Activity and Schools -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Recent Schools -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Schools</h3>
                </div>
                <div class="p-6">
                    {% if recent_schools %}
                    <div class="space-y-4">
                        {% for school in recent_schools %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="building" class="w-5 h-5 text-purple-600"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ school.name }}</p>
                                    <p class="text-xs text-gray-500">{{ school.created_at|date:"M d, Y" }}</p>
                                </div>
                            </div>
                            <a href="{% url 'site_admin_school_detail' school.id %}" class="text-purple-600 hover:text-purple-900">
                                <i data-lucide="arrow-right" class="w-4 h-4"></i>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-gray-500 text-center py-4">No schools registered yet.</p>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Users -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Users</h3>
                </div>
                <div class="p-6">
                    {% if recent_users %}
                    <div class="space-y-4">
                        {% for user in recent_users %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 text-sm font-medium">
                                        {{ user.first_name|first|default:"U" }}{{ user.last_name|first|default:"" }}
                                    </span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                                    <p class="text-xs text-gray-500">{{ user.date_joined|date:"M d, Y" }}</p>
                                </div>
                            </div>
                            <a href="{% url 'site_admin_user_detail' user.id %}" class="text-blue-600 hover:text-blue-900">
                                <i data-lucide="arrow-right" class="w-4 h-4"></i>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-gray-500 text-center py-4">No users registered yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Schools Overview -->
        {% if schools_with_stats %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Schools Overview</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">School</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Admin</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teachers</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for school in schools_with_stats %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <i data-lucide="building" class="w-5 h-5 text-purple-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ school.name }}</div>
                                        <div class="text-sm text-gray-500">{{ school.current_term }} {{ school.academic_year }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ school.admin.get_full_name|default:school.admin.username }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ school.student_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ school.teacher_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ school.class_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ school.created_at|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{% url 'site_admin_school_detail' school.id %}" 
                                   class="text-purple-600 hover:text-purple-900" title="View Details">
                                    <i data-lucide="eye" class="w-4 h-4"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-5 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="building" class="w-6 h-6 text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">Manage Schools</h4>
                        <p class="text-sm text-gray-600">View all schools</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_schools' %}" class="w-full bg-purple-600 text-white text-center py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                        View All Schools
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">Manage Users</h4>
                        <p class="text-sm text-gray-600">View all users</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_users' %}" class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                        View All Users
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="bar-chart-3" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">Analytics</h4>
                        <p class="text-sm text-gray-600">System analytics</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_analytics' %}" class="w-full bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                        View Analytics
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="image" class="w-6 h-6 text-orange-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">Hero Banners</h4>
                        <p class="text-sm text-gray-600">Manage homepage</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'hero_banner_management' %}" class="w-full bg-orange-600 text-white text-center py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors">
                        Manage Banners
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="server" class="w-6 h-6 text-gray-600"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-semibold text-gray-900">System Settings</h4>
                        <p class="text-sm text-gray-600">Configure system</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="{% url 'site_admin_system_settings' %}" class="w-full bg-gray-600 text-white text-center py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors">
                        System Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize Lucide icons
    lucide.createIcons();
</script>
{% endblock %}
