from django.contrib import admin
from .models import *


@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    """Site-wide settings - only accessible to superusers"""

    def has_add_permission(self, request):
        # Only allow one instance
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion
        return False

    def has_module_permission(self, request):
        # Only superusers can access
        return request.user.is_superuser

    fieldsets = (
        ('Email Configuration (SMTP)', {
            'fields': ('email_host', 'email_port', 'email_host_user', 'email_host_password', 'email_use_tls', 'email_use_ssl'),
            'description': 'Configure SMTP settings for all schools. Schools will use these settings with their own branding.'
        }),
        ('Site Information', {
            'fields': ('site_name', 'support_email'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )

    readonly_fields = ['created_at', 'updated_at']

    def save_model(self, request, obj, form, change):
        """Update Django settings when site settings are saved"""
        super().save_model(request, obj, form, change)

        # Update Django settings in memory
        from django.conf import settings
        settings.EMAIL_HOST = obj.email_host
        settings.EMAIL_PORT = obj.email_port
        settings.EMAIL_HOST_USER = obj.email_host_user
        settings.EMAIL_HOST_PASSWORD = obj.email_host_password
        settings.EMAIL_USE_TLS = obj.email_use_tls
        settings.EMAIL_USE_SSL = obj.email_use_ssl

@admin.register(School)
class SchoolAdmin(admin.ModelAdmin):
    list_display = ['name', 'current_term', 'academic_year', 'created_at']
    search_fields = ['name']


@admin.register(SchoolSettings)
class SchoolSettingsAdmin(admin.ModelAdmin):
    list_display = ['school', 'grading_system', 'send_welcome_emails', 'send_password_reset_emails', 'updated_at']
    list_filter = ['grading_system', 'send_welcome_emails', 'send_password_reset_emails']
    search_fields = ['school__name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('School', {
            'fields': ('school',)
        }),
        ('Notification Settings', {
            'fields': ('send_welcome_emails', 'send_password_reset_emails', 'send_grade_notifications'),
            'description': 'Control email notifications sent to school members. SMTP configuration is managed by site admin.'
        }),
        ('Academic Settings', {
            'fields': ('grading_system', 'auto_calculate_positions', 'allow_score_editing_after_submission', 'require_teacher_comments'),
            'classes': ('collapse',)
        }),
        ('Report Settings', {
            'fields': ('default_report_template', 'include_school_logo_in_reports', 'show_class_position', 'show_overall_position'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(SchoolGradingScale)
class SchoolGradingScaleAdmin(admin.ModelAdmin):
    list_display = ['school', 'grading_type', 'created_at']
    list_filter = ['grading_type']

@admin.register(SchoolGradeRange)
class SchoolGradeRangeAdmin(admin.ModelAdmin):
    list_display = ['grading_scale', 'grade', 'min_score', 'max_score', 'remark', 'order']
    list_filter = ['grading_scale']
    ordering = ['grading_scale', 'order']

@admin.register(SchoolGradingField)
class SchoolGradingFieldAdmin(admin.ModelAdmin):
    list_display = ['school', 'name', 'code', 'percentage', 'max_score', 'is_active', 'order']
    list_filter = ['school', 'is_active']
    ordering = ['school', 'order']

@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    list_display = ['school', 'name', 'is_active', 'created_at']
    list_filter = ['school', 'is_active']
    search_fields = ['name']

@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ['school', 'name', 'code', 'is_active', 'created_at']
    list_filter = ['school', 'is_active']
    search_fields = ['name', 'code']

@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'student_id', 'gender', 'current_class', 'parent_phone', 'is_active']
    list_filter = ['school', 'current_class', 'gender', 'is_active']
    search_fields = ['first_name', 'last_name', 'student_id', 'parent_phone']

@admin.register(Score)
class ScoreAdmin(admin.ModelAdmin):
    list_display = ['student', 'subject', 'term', 'academic_year', 'total_score', 'grade']
    list_filter = ['term', 'academic_year', 'subject']
    search_fields = ['student__first_name', 'student__last_name']

@admin.register(StudentTermResult)
class StudentTermResultAdmin(admin.ModelAdmin):
    list_display = ['student', 'term', 'academic_year', 'average_score', 'position', 'is_approved', 'sms_sent']
    list_filter = ['term', 'academic_year', 'is_approved', 'sms_sent']
    search_fields = ['student__first_name', 'student__last_name']

@admin.register(HeroBanner)
class HeroBannerAdmin(admin.ModelAdmin):
    list_display = ['title', 'is_active', 'background_color', 'show_stats', 'created_at']
    list_filter = ['is_active', 'background_color', 'show_stats', 'show_floating_cards']
    search_fields = ['title', 'subtitle']
    fieldsets = (
        ('Content', {
            'fields': ('title', 'subtitle', 'description')
        }),
        ('Buttons', {
            'fields': (
                ('primary_button_text', 'primary_button_url'),
                ('secondary_button_text', 'secondary_button_url')
            )
        }),
        ('Background & Images', {
            'fields': ('background_color', 'gradient_colors', 'background_image', 'image_url')
        }),
        ('Display Options', {
            'fields': ('show_stats', 'show_floating_cards', 'is_active')
        }),
    )

    def save_model(self, request, obj, form, change):
        # Ensure only one banner is active at a time
        if obj.is_active:
            HeroBanner.objects.filter(is_active=True).exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)
