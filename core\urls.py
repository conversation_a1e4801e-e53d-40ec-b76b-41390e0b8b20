from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from . import teacher_views
from . import admin_views
from . import site_admin_views
from . import school_admin_views
from . import school_admin_config_views

urlpatterns = [
    # Homepage
    path('', views.homepage, name='homepage'),

    # Authentication
    path('login/', views.login_view, name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('register/', views.register_view, name='register'),
    path('register/school-admin/', views.school_admin_register_view, name='school_admin_register'),

    # School Setup Wizard
    path('setup/', views.school_setup_wizard, name='school_setup_wizard'),
    path('setup/profile/', views.school_setup_profile, name='school_setup_profile'),
    path('setup/grading/', views.school_setup_grading, name='school_setup_grading'),
    path('setup/classes/', views.school_setup_classes, name='school_setup_classes'),

    # Main Dashboard (redirects based on user type)
    path('dashboard/', views.main_dashboard, name='dashboard'),

    # Profile Management
    path('profile/setup/', views.profile_setup_view, name='profile_setup'),
    path('profile/edit/', views.profile_edit_view, name='profile_edit'),

    # Site Admin Routes (Superuser only)
    path('site-admin/dashboard/', site_admin_views.site_admin_dashboard, name='site_admin_dashboard'),
    path('site-admin/schools/', site_admin_views.site_admin_schools, name='site_admin_schools'),
    path('site-admin/schools/<int:school_id>/', site_admin_views.site_admin_school_detail, name='site_admin_school_detail'),
    path('site-admin/users/', site_admin_views.site_admin_users, name='site_admin_users'),
    path('site-admin/users/<int:user_id>/', site_admin_views.site_admin_user_detail, name='site_admin_user_detail'),
    path('site-admin/analytics/', site_admin_views.site_admin_analytics, name='site_admin_analytics'),
    path('site-admin/system-settings/', site_admin_views.site_admin_system_settings, name='site_admin_system_settings'),

    # System Templates Management
    path('site-admin/class-templates/', site_admin_views.system_class_templates, name='system_class_templates'),
    path('site-admin/class-templates/add/', site_admin_views.add_class_template, name='add_class_template'),
    path('site-admin/class-templates/<int:template_id>/edit/', site_admin_views.edit_class_template, name='edit_class_template'),
    path('site-admin/subject-templates/', site_admin_views.system_subject_templates, name='system_subject_templates'),
    path('site-admin/subject-templates/add/', site_admin_views.add_subject_template, name='add_subject_template'),
    path('site-admin/subject-templates/<int:template_id>/edit/', site_admin_views.edit_subject_template, name='edit_subject_template'),
    path('site-admin/grading-templates/', site_admin_views.system_grading_templates, name='system_grading_templates'),
    path('site-admin/grading-templates/add/', site_admin_views.add_grading_template, name='add_grading_template'),
    path('site-admin/grading-templates/<int:template_id>/edit/', site_admin_views.edit_grading_template, name='edit_grading_template'),

    # System Maintenance
    path('site-admin/maintenance/', site_admin_views.system_maintenance, name='system_maintenance'),
    path('site-admin/maintenance/clear-cache/', site_admin_views.clear_cache, name='clear_cache'),
    path('site-admin/maintenance/backup/', site_admin_views.database_backup, name='database_backup'),
    path('site-admin/maintenance/health-check/', site_admin_views.system_health_check, name='system_health_check'),
    path('site-admin/maintenance/download-backup/<str:filename>/', site_admin_views.download_backup, name='download_backup'),
    path('site-admin/maintenance/restore-backup/', site_admin_views.restore_backup, name='restore_backup'),
    path('site-admin/maintenance/delete-backup/<str:filename>/', site_admin_views.delete_backup, name='delete_backup'),

    # Hero Banner Management
    path('site-admin/hero-banners/', site_admin_views.hero_banner_management, name='hero_banner_management'),
    path('site-admin/hero-banners/add/', site_admin_views.add_hero_banner, name='add_hero_banner'),
    path('site-admin/hero-banners/<int:banner_id>/edit/', site_admin_views.edit_hero_banner, name='edit_hero_banner'),
    path('site-admin/hero-banners/<int:banner_id>/delete/', site_admin_views.delete_hero_banner, name='delete_hero_banner'),
    path('site-admin/hero-banners/<int:banner_id>/activate/', site_admin_views.activate_hero_banner, name='activate_hero_banner'),

    # Report Card Templates
    path('site-admin/report-templates/', site_admin_views.system_report_templates, name='system_report_templates'),
    path('site-admin/report-templates/add/', site_admin_views.add_report_template, name='add_report_template'),
    path('site-admin/report-templates/<int:template_id>/edit/', site_admin_views.edit_report_template, name='edit_report_template'),
    path('site-admin/report-templates/<int:template_id>/preview/', site_admin_views.preview_report_template, name='preview_report_template'),

    # System Terms
    path('site-admin/terms/', site_admin_views.system_terms, name='system_terms'),
    path('site-admin/terms/add/', site_admin_views.add_system_term, name='add_system_term'),
    path('site-admin/terms/<int:term_id>/edit/', site_admin_views.edit_system_term, name='edit_system_term'),

    # School Admin Routes
    path('school-admin/dashboard/', school_admin_views.school_admin_dashboard, name='admin_dashboard'),
    path('school-admin/teachers/', school_admin_views.manage_teachers, name='manage_teachers'),
    path('school-admin/teachers/add/', school_admin_views.add_teacher, name='add_teacher'),
    path('school-admin/teachers/assign/', school_admin_views.assign_teacher, name='assign_teacher'),
    path('school-admin/users/<int:user_id>/reset-password/', school_admin_views.reset_user_password, name='reset_user_password'),
    path('school-admin/teachers/assign/', school_admin_views.assign_teacher, name='assign_teacher'),
    path('school-admin/teachers/<int:teacher_id>/', school_admin_views.teacher_detail, name='teacher_detail'),
    path('school-admin/teachers/<int:teacher_id>/edit/', school_admin_views.teacher_edit, name='teacher_edit'),
    path('school-admin/teachers/<int:teacher_id>/reset-password/', school_admin_views.teacher_reset_password, name='teacher_reset_password'),
    path('school-admin/teachers/<int:teacher_id>/toggle-status/', school_admin_views.teacher_toggle_status, name='teacher_toggle_status'),
    path('school-admin/teachers/<int:teacher_id>/remove/', school_admin_views.teacher_remove, name='teacher_remove'),
    path('school-admin/reports/', school_admin_views.school_reports, name='school_reports'),
    path('school-admin/reports/class/<int:class_id>/download/', school_admin_views.download_class_reports, name='download_class_reports'),
    path('school-admin/reports/student/', school_admin_views.generate_student_report, name='generate_student_report'),
    path('school-admin/reports/student/<int:student_id>/', school_admin_views.generate_individual_report, name='generate_individual_report'),
    path('school-admin/reports/bulk-student/', school_admin_views.bulk_student_reports, name='bulk_student_reports'),
    path('school-admin/reports/student-performance/', school_admin_views.student_performance_analysis, name='student_performance_analysis'),
    path('school-admin/reports/class-performance/', school_admin_views.class_performance_report, name='class_performance_report'),
    path('school-admin/reports/class-ranking/', school_admin_views.class_ranking_report, name='class_ranking_report'),
    path('school-admin/reports/subject-analysis/', school_admin_views.subject_analysis_report, name='subject_analysis_report'),
    path('school-admin/reports/school-summary/', school_admin_views.school_summary_report, name='school_summary_report'),
    path('school-admin/reports/teacher-performance/', school_admin_views.teacher_performance_report, name='teacher_performance_report'),
    path('school-admin/reports/attendance/', school_admin_views.attendance_report, name='attendance_report'),
    path('school-admin/settings/', school_admin_views.school_settings, name='school_settings'),
    path('school-admin/test-email/', school_admin_views.test_email_configuration, name='test_email_configuration'),
    path('school-admin/students/', school_admin_views.manage_students, name='manage_students'),
    path('school-admin/students/template/', school_admin_views.download_student_template, name='download_student_template'),
    path('school-admin/students/add/', views.student_add_view, name='student_add'),
    path('school-admin/students/<int:student_id>/', views.student_detail_view, name='student_detail'),
    path('school-admin/students/<int:student_id>/edit/', views.student_edit_view, name='student_edit'),
    path('school-admin/students/<int:student_id>/delete/', views.student_delete_view, name='student_delete'),
    path('school-admin/students/bulk-upload/', views.students_bulk_upload_view, name='students_bulk_upload'),
    path('ajax/bulk-upload-students/', views.ajax_bulk_upload_students, name='ajax_bulk_upload_students'),
    path('school-admin/subjects/', views.subjects_list_view, name='subjects_list'),
    path('school-admin/subjects/add/', views.subject_add_view, name='subject_add'),
    path('school-admin/subjects/<int:subject_id>/edit/', views.subject_edit_view, name='subject_edit'),
    path('school-admin/subjects/<int:subject_id>/delete/', school_admin_views.delete_subject, name='delete_subject'),
    path('school-admin/classes/', views.classes_list_view, name='classes_list'),
    path('school-admin/classes/add/', views.class_add_view, name='class_add'),
    path('school-admin/classes/<int:class_id>/', views.class_detail_view, name='class_detail'),
    path('school-admin/classes/<int:class_id>/edit/', views.class_edit_view, name='class_edit'),
    path('school-admin/report-templates/', school_admin_views.report_templates, name='report_templates'),
    path('school-admin/report-templates/<int:template_id>/select/', school_admin_views.select_report_template, name='select_report_template'),
    path('school-admin/report-templates/<int:template_id>/preview/', school_admin_views.preview_report_template, name='preview_report_template'),
    path('school-admin/preview-template/<int:template_id>/', school_admin_views.preview_report_template, name='preview_template_ajax'),
    path('school-admin/grading-fields/', school_admin_views.grading_fields_setup, name='grading_fields_setup'),
    path('school-admin/term-config/', school_admin_views.term_configuration, name='term_configuration'),
    path('school-admin/scores/overview/', admin_views.admin_score_overview, name='admin_score_overview'),
    path('school-admin/scores/class/<int:class_id>/subject/<int:subject_id>/', views.score_entry_view, name='score_entry'),
    path('school-admin/scores/bulk/<int:class_id>/', views.bulk_score_entry_view, name='bulk_score_entry'),
    path('school-admin/reports/class/<int:class_id>/', views.class_report_view, name='class_report'),

    # School Admin Configuration URLs
    path('school-admin/config/grading/', school_admin_config_views.grading_config_dashboard, name='grading_config_dashboard'),
    path('school-admin/config/grading/scale/', school_admin_config_views.grading_scale_setup, name='grading_scale_setup'),
    path('school-admin/config/grading/ranges/', school_admin_config_views.grade_ranges_setup, name='grade_ranges_setup'),
    path('school-admin/config/grading/fields/', school_admin_config_views.grading_fields_setup, name='grading_fields_setup'),
    path('school-admin/config/terms/', school_admin_config_views.term_setup, name='term_setup'),
    path('school-admin/config/terms/add/', school_admin_config_views.add_term, name='add_term'),
    path('school-admin/config/terms/<int:term_id>/edit/', school_admin_config_views.edit_term, name='edit_term'),
    path('school-admin/config/classes-subjects/', school_admin_config_views.class_subject_setup, name='class_subject_setup'),
    path('school-admin/config/classes/add-from-template/', school_admin_config_views.add_class_from_template, name='add_class_from_template'),
    path('school-admin/config/subjects/add-from-template/', school_admin_config_views.add_subject_from_template, name='add_subject_from_template'),

    # Teacher Routes
    path('teacher/dashboard/', teacher_views.teacher_dashboard, name='teacher_dashboard'),
    path('teacher/class/<int:class_id>/', teacher_views.teacher_class_subjects, name='teacher_class_subjects'),
    path('teacher/class/<int:class_id>/subject/<int:subject_id>/scores/', teacher_views.teacher_score_entry, name='teacher_score_entry'),
    path('teacher/class/<int:class_id>/reports/', teacher_views.teacher_class_reports, name='teacher_class_reports'),
    path('teacher/student/<int:student_id>/report/', teacher_views.teacher_student_report, name='teacher_student_report'),
    path('teacher/class/<int:class_id>/subject/<int:subject_id>/submitted-scores/', teacher_views.teacher_submitted_scores, name='teacher_submitted_scores'),
    path('teacher/class/<int:class_id>/subject/<int:subject_id>/export/', teacher_views.teacher_export_scores, name='teacher_export_scores'),
    path('teacher/class/<int:class_id>/manage/', teacher_views.class_teacher_management, name='class_teacher_management'),
    path('teacher/class/<int:class_id>/students/', teacher_views.student_management, name='student_management'),
    path('teacher/class/<int:class_id>/all-subjects/', teacher_views.teacher_all_subjects_access, name='teacher_all_subjects_access'),

    # Setup Routes (for initial school setup)
    path('setup/', views.setup_view, name='setup'),
    path('setup/school/', views.school_setup_view, name='school_setup'),
    path('setup/grading/', views.grading_setup_view, name='grading_setup'),
    path('setup/classes/', views.classes_setup_view, name='classes_setup'),
    path('setup/subjects/', views.subjects_setup_view, name='subjects_setup'),
    path('setup/complete/', views.setup_complete_view, name='setup_complete'),

    # Legacy Student Management URLs (removed - use school-admin URLs instead)

    # Settings (Admin only)
    path('admin/settings/', views.settings_view, name='settings'),
    path('admin/settings/term/', views.term_config_view, name='term_config'),

    # Export (School Admin only)
    path('school-admin/export/students/', views.export_students_excel, name='export_students'),
    path('school-admin/export/scores/', views.export_scores_excel, name='export_scores'),

    # Legacy routes for backward compatibility
    path('scores/', views.scores_dashboard_view, name='scores_dashboard'),
    path('analytics/', views.analytics_dashboard, name='analytics_dashboard'),
]
