{% extends 'base.html' %}
{% load static %}
{% load score_filters %}

{% block title %}Generate Report - {{ student.full_name }} - {{ school.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{% url 'generate_student_report' %}?class_id={{ student.current_class.id }}" 
                       class="text-gray-600 hover:text-gray-900 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5"></i>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Generate Report Card</h1>
                        <p class="mt-2 text-gray-600">{{ student.full_name }} - {{ student.current_class.name }}</p>
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    <span class="font-medium">{{ current_term }}</span> {{ academic_year }}
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Student Info & Actions -->
            <div class="lg:col-span-1">
                <!-- Student Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Student Information</h2>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-gray-500">Full Name</label>
                            <p class="text-gray-900">{{ student.full_name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Student ID</label>
                            <p class="text-gray-900">{{ student.student_id }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Class</label>
                            <p class="text-gray-900">{{ student.current_class.name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Total Subjects</label>
                            <p class="text-gray-900">{{ total_subjects }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Average Score</label>
                            <p class="text-gray-900 font-semibold">{{ average_score|floatformat:1 }}%</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Position</label>
                            <p class="text-gray-900 font-semibold">{{ term_result.position|default:"Not calculated" }}</p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
                    
                    <form method="post" class="space-y-4">
                        {% csrf_token %}
                        
                        <!-- Report Options -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Report Options</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="include_photo" name="include_photo" checked
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <label for="include_photo" class="ml-2 text-sm text-gray-700">Include photo</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="include_comments" name="include_comments" checked
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <label for="include_comments" class="ml-2 text-sm text-gray-700">Include comments</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="include_signatures" name="include_signatures" checked
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <label for="include_signatures" class="ml-2 text-sm text-gray-700">Include signatures</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="space-y-3">
                            <button type="submit" name="action" value="download_pdf"
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                                Download PDF
                            </button>
                            <button type="button" onclick="window.print()"
                                    class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                <i data-lucide="printer" class="w-4 h-4 inline mr-2"></i>
                                Print Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Report Preview -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-lg font-semibold text-gray-900">Report Preview</h2>
                        <div class="text-sm text-gray-500">
                            Template: {{ school.report_template.name|default:"Default Template" }}
                        </div>
                    </div>
                    
                    <!-- Report Card Preview - Compact Professional Layout -->
                    {% if school.report_template %}
                        {% if school.report_template.design_style == 'classic' and school.report_template.color_scheme == 'blue' %}
                            <div class="border-4 border-blue-600 p-3 bg-white" style="min-height: 800px;">
                                <!-- Compact Professional Header -->
                                <div class="mb-3">
                                    <!-- Header Section with Logo, School Info, and Student Photo -->
                                    <div class="flex items-center justify-between mb-3">
                                        <!-- School Logo -->
                                        <div class="school-logo">
                                            {% if school_logo_base64 %}
                                            <img src="data:image/png;base64,{{ school_logo_base64 }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-blue-600">
                                            {% elif school.logo %}
                                            <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-blue-600">
                                            {% else %}
                                            <div class="w-16 h-16 border-2 border-blue-600 flex items-center justify-center bg-blue-50">
                                                <i data-lucide="school" class="w-8 h-8 text-blue-600"></i>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- School Information -->
                                        <div class="flex-1 text-center mx-4">
                                            <h1 class="text-xl font-bold text-blue-600 uppercase">{{ school.name }}</h1>
                                            <div class="text-xs font-semibold text-gray-700">SCHOOL COMPLEX</div>
                                            {% if school.address %}
                                            <div class="text-xs text-gray-600">{{ school.address }}</div>
                                            {% endif %}
                                            {% if school.phone_number %}
                                            <div class="text-xs text-gray-600">{{ school.phone_number }}</div>
                                            {% endif %}
                                            {% if school.admin.email %}
                                            <div class="text-xs text-gray-600">{{ school.admin.email }}</div>
                                            {% endif %}
                                        </div>

                                        <!-- Student Photo -->
                                        <div class="student-photo">
                                            {% if student_photo_base64 %}
                                            <img src="data:image/jpeg;base64,{{ student_photo_base64 }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-blue-600">
                                            {% elif student.profile_picture %}
                                            <img src="{{ student.profile_picture.url }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-blue-600">
                                            {% else %}
                                            <div class="w-16 h-20 border-2 border-blue-600 flex items-center justify-center bg-blue-50">
                                                <div class="text-center">
                                                    <i data-lucide="user" class="w-6 h-6 text-blue-600 mx-auto"></i>
                                                    <div class="text-xs text-blue-600 mt-1">STUDENT<br>PHOTO</div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Report Title -->
                                    <div class="bg-blue-600 text-white text-center py-2 font-bold text-sm uppercase">
                                        TERMINAL REPORT SHEET
                                    </div>
                                </div>
                        {% elif school.report_template.color_scheme == 'green' %}
                            <div class="border-4 border-green-600 p-3 bg-white" style="min-height: 800px;">
                                <!-- Compact Professional Header - Green -->
                                <div class="mb-3">
                                    <!-- Header Section with Logo, School Info, and Student Photo -->
                                    <div class="flex items-center justify-between mb-3">
                                        <!-- School Logo -->
                                        <div class="school-logo">
                                            {% if school_logo_base64 %}
                                            <img src="data:image/png;base64,{{ school_logo_base64 }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-green-600">
                                            {% elif school.logo %}
                                            <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-green-600">
                                            {% else %}
                                            <div class="w-16 h-16 border-2 border-green-600 flex items-center justify-center bg-green-50">
                                                <i data-lucide="school" class="w-8 h-8 text-green-600"></i>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- School Information -->
                                        <div class="flex-1 text-center mx-4">
                                            <h1 class="text-xl font-bold text-green-600 uppercase">{{ school.name }}</h1>
                                            <div class="text-xs font-semibold text-gray-700">SCHOOL COMPLEX</div>
                                            {% if school.address %}
                                            <div class="text-xs text-gray-600">{{ school.address }}</div>
                                            {% endif %}
                                            {% if school.phone_number %}
                                            <div class="text-xs text-gray-600">{{ school.phone_number }}</div>
                                            {% endif %}
                                            {% if school.admin.email %}
                                            <div class="text-xs text-gray-600">{{ school.admin.email }}</div>
                                            {% endif %}
                                        </div>

                                        <!-- Student Photo -->
                                        <div class="student-photo">
                                            {% if student_photo_base64 %}
                                            <img src="data:image/jpeg;base64,{{ student_photo_base64 }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-green-600">
                                            {% elif student.profile_picture %}
                                            <img src="{{ student.profile_picture.url }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-green-600">
                                            {% else %}
                                            <div class="w-16 h-20 border-2 border-green-600 flex items-center justify-center bg-green-50">
                                                <div class="text-center">
                                                    <i data-lucide="user" class="w-6 h-6 text-green-600 mx-auto"></i>
                                                    <div class="text-xs text-green-600 mt-1">STUDENT<br>PHOTO</div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Report Title -->
                                    <div class="bg-green-600 text-white text-center py-2 font-bold text-sm uppercase">
                                        TERMINAL REPORT SHEET
                                    </div>
                                </div>

                                <!-- Compact Student Information Section -->
                                <div class="p-3 border-b border-gray-300 mb-3">
                                    <div class="grid grid-cols-2 gap-6 text-xs">
                                        <!-- Left Column -->
                                        <div class="space-y-1">
                                            <div class="flex">
                                                <span class="font-bold w-20 border-b border-black">Student ID:</span>
                                                <span class="flex-1 border-b border-black pl-2">{{ student.student_id }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-20 border-b border-black">Name:</span>
                                                <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.full_name }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-20 border-b border-black">Class:</span>
                                                <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.current_class.name }}</span>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="space-y-1">
                                            <div class="flex">
                                                <span class="font-bold w-24 border-b border-black">Academic Year:</span>
                                                <span class="flex-1 border-b border-black pl-2">{{ academic_year }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-24 border-b border-black">Academic Term:</span>
                                                <span class="flex-1 border-b border-black pl-2 uppercase">{{ current_term }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-24 border-b border-black">School reopens:</span>
                                                <span class="flex-1 border-b border-black pl-2">JUNE 05, 19</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        {% else %}
                            <div class="border-4 border-blue-600 p-3 bg-white" style="min-height: 800px;">
                                <!-- Compact Professional Header - Default Blue -->
                                <div class="mb-3">
                                    <!-- Header Section with Logo, School Info, and Student Photo -->
                                    <div class="flex items-center justify-between mb-3">
                                        <!-- School Logo -->
                                        <div class="school-logo">
                                            {% if school_logo_base64 %}
                                            <img src="data:image/png;base64,{{ school_logo_base64 }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-blue-600">
                                            {% elif school.logo %}
                                            <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-blue-600">
                                            {% else %}
                                            <div class="w-16 h-16 border-2 border-blue-600 flex items-center justify-center bg-blue-50">
                                                <i data-lucide="school" class="w-8 h-8 text-blue-600"></i>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- School Information -->
                                        <div class="flex-1 text-center mx-4">
                                            <h1 class="text-xl font-bold text-blue-600 uppercase">{{ school.name }}</h1>
                                            <div class="text-xs font-semibold text-gray-700">SCHOOL COMPLEX</div>
                                            {% if school.address %}
                                            <div class="text-xs text-gray-600">{{ school.address }}</div>
                                            {% endif %}
                                            {% if school.phone_number %}
                                            <div class="text-xs text-gray-600">{{ school.phone_number }}</div>
                                            {% endif %}
                                            {% if school.admin.email %}
                                            <div class="text-xs text-gray-600">{{ school.admin.email }}</div>
                                            {% endif %}
                                        </div>

                                        <!-- Student Photo -->
                                        <div class="student-photo">
                                            {% if student_photo_base64 %}
                                            <img src="data:image/jpeg;base64,{{ student_photo_base64 }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-blue-600">
                                            {% elif student.profile_picture %}
                                            <img src="{{ student.profile_picture.url }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-blue-600">
                                            {% else %}
                                            <div class="w-16 h-20 border-2 border-blue-600 flex items-center justify-center bg-blue-50">
                                                <div class="text-center">
                                                    <i data-lucide="user" class="w-6 h-6 text-blue-600 mx-auto"></i>
                                                    <div class="text-xs text-blue-600 mt-1">STUDENT<br>PHOTO</div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <!-- Report Title -->
                                    <div class="bg-blue-600 text-white text-center py-2 font-bold text-sm uppercase">
                                        TERMINAL REPORT SHEET
                                    </div>
                                </div>

                                <!-- Compact Student Information Section -->
                                <div class="p-3 border-b border-gray-300 mb-3">
                                    <div class="grid grid-cols-2 gap-6 text-xs">
                                        <!-- Left Column -->
                                        <div class="space-y-1">
                                            <div class="flex">
                                                <span class="font-bold w-20 border-b border-black">Student ID:</span>
                                                <span class="flex-1 border-b border-black pl-2">{{ student.student_id }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-20 border-b border-black">Name:</span>
                                                <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.full_name }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-20 border-b border-black">Class:</span>
                                                <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.current_class.name }}</span>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="space-y-1">
                                            <div class="flex">
                                                <span class="font-bold w-24 border-b border-black">Academic Year:</span>
                                                <span class="flex-1 border-b border-black pl-2">{{ academic_year }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-24 border-b border-black">Academic Term:</span>
                                                <span class="flex-1 border-b border-black pl-2 uppercase">{{ current_term }}</span>
                                            </div>
                                            <div class="flex">
                                                <span class="font-bold w-24 border-b border-black">School reopens:</span>
                                                <span class="flex-1 border-b border-black pl-2">JUNE 05, 19</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        {% endif %}
                    {% else %}
                        <div class="border-4 border-blue-600 p-3 bg-white" style="min-height: 800px;">
                            <!-- Compact Professional Header - Fallback -->
                            <div class="mb-3">
                                <!-- Header Section with Logo, School Info, and Student Photo -->
                                <div class="flex items-center justify-between mb-3">
                                    <!-- School Logo -->
                                    <div class="school-logo">
                                        {% if school_logo_base64 %}
                                        <img src="data:image/png;base64,{{ school_logo_base64 }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-blue-600">
                                        {% elif school.logo %}
                                        <img src="{{ school.logo.url }}" alt="{{ school.name }}" class="w-16 h-16 object-contain border-2 border-blue-600">
                                        {% else %}
                                        <div class="w-16 h-16 border-2 border-blue-600 flex items-center justify-center bg-blue-50">
                                            <i data-lucide="school" class="w-8 h-8 text-blue-600"></i>
                                        </div>
                                        {% endif %}
                                    </div>

                                    <!-- School Information -->
                                    <div class="flex-1 text-center mx-4">
                                        <h1 class="text-xl font-bold text-blue-600 uppercase">{{ school.name }}</h1>
                                        <div class="text-xs font-semibold text-gray-700">SCHOOL COMPLEX</div>
                                        {% if school.address %}
                                        <div class="text-xs text-gray-600">{{ school.address }}</div>
                                        {% endif %}
                                        {% if school.phone_number %}
                                        <div class="text-xs text-gray-600">{{ school.phone_number }}</div>
                                        {% endif %}
                                        {% if school.admin.email %}
                                        <div class="text-xs text-gray-600">{{ school.admin.email }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- Student Photo -->
                                    <div class="student-photo">
                                        {% if student_photo_base64 %}
                                        <img src="data:image/jpeg;base64,{{ student_photo_base64 }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-blue-600">
                                        {% elif student.profile_picture %}
                                        <img src="{{ student.profile_picture.url }}" alt="{{ student.full_name }}" class="w-16 h-20 object-cover border-2 border-blue-600">
                                        {% else %}
                                        <div class="w-16 h-20 border-2 border-blue-600 flex items-center justify-center bg-blue-50">
                                            <div class="text-center">
                                                <i data-lucide="user" class="w-6 h-6 text-blue-600 mx-auto"></i>
                                                <div class="text-xs text-blue-600 mt-1">STUDENT<br>PHOTO</div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Report Title -->
                                <div class="bg-blue-600 text-white text-center py-2 font-bold text-sm uppercase">
                                    TERMINAL REPORT SHEET
                                </div>
                            </div>

                            <!-- Compact Student Information Section -->
                            <div class="p-3 border-b border-gray-300 mb-3">
                                <div class="grid grid-cols-2 gap-6 text-xs">
                                    <!-- Left Column -->
                                    <div class="space-y-1">
                                        <div class="flex">
                                            <span class="font-bold w-20 border-b border-black">Student ID:</span>
                                            <span class="flex-1 border-b border-black pl-2">{{ student.student_id }}</span>
                                        </div>
                                        <div class="flex">
                                            <span class="font-bold w-20 border-b border-black">Name:</span>
                                            <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.full_name }}</span>
                                        </div>
                                        <div class="flex">
                                            <span class="font-bold w-20 border-b border-black">Class:</span>
                                            <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.current_class.name }}</span>
                                        </div>
                                    </div>

                                    <!-- Right Column -->
                                    <div class="space-y-1">
                                        <div class="flex">
                                            <span class="font-bold w-24 border-b border-black">Academic Year:</span>
                                            <span class="flex-1 border-b border-black pl-2">{{ academic_year }}</span>
                                        </div>
                                        <div class="flex">
                                            <span class="font-bold w-24 border-b border-black">Academic Term:</span>
                                            <span class="flex-1 border-b border-black pl-2 uppercase">{{ current_term }}</span>
                                        </div>
                                        <div class="flex">
                                            <span class="font-bold w-24 border-b border-black">School reopens:</span>
                                            <span class="flex-1 border-b border-black pl-2">JUNE 05, 19</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    {% endif %}

                        <!-- Compact Student Information Section -->
                        <div class="p-3 border-b border-gray-300 mb-3">
                            <div class="grid grid-cols-2 gap-6 text-xs">
                                <!-- Left Column -->
                                <div class="space-y-1">
                                    <div class="flex">
                                        <span class="font-bold w-20 border-b border-black">Student ID:</span>
                                        <span class="flex-1 border-b border-black pl-2">{{ student.student_id }}</span>
                                    </div>
                                    <div class="flex">
                                        <span class="font-bold w-20 border-b border-black">Name:</span>
                                        <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.full_name }}</span>
                                    </div>
                                    <div class="flex">
                                        <span class="font-bold w-20 border-b border-black">Class:</span>
                                        <span class="flex-1 border-b border-black pl-2 uppercase">{{ student.current_class.name }}</span>
                                    </div>
                                </div>

                                <!-- Right Column -->
                                <div class="space-y-1">
                                    <div class="flex">
                                        <span class="font-bold w-24 border-b border-black">Academic Year:</span>
                                        <span class="flex-1 border-b border-black pl-2">{{ academic_year }}</span>
                                    </div>
                                    <div class="flex">
                                        <span class="font-bold w-24 border-b border-black">Academic Term:</span>
                                        <span class="flex-1 border-b border-black pl-2 uppercase">{{ current_term }}</span>
                                    </div>
                                    <div class="flex">
                                        <span class="font-bold w-24 border-b border-black">School reopens:</span>
                                        <span class="flex-1 border-b border-black pl-2">JUNE 05, 19</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Compact Academic Performance Table -->
                        <div class="mb-3">
                            <div class="bg-blue-600 text-white text-center py-1 font-bold text-xs uppercase mb-2">
                                ACADEMIC PERFORMANCE
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full border-collapse border border-gray-400 text-xs">
                                    <thead class="bg-blue-600 text-white">
                                        <tr>
                                            <th class="border border-gray-400 px-2 py-1 text-left font-bold">Subject</th>
                                            {% for field in school.grading_fields.all|dictsort:"order" %}
                                            {% if field.is_active %}
                                            <th class="border border-gray-400 px-2 py-1 text-center font-bold">{{ field.name }}</th>
                                            {% endif %}
                                            {% endfor %}
                                            <th class="border border-gray-400 px-2 py-1 text-center font-bold">Total</th>
                                            <th class="border border-gray-400 px-2 py-1 text-center font-bold">Grade</th>
                                            <th class="border border-gray-400 px-2 py-1 text-center font-bold">Position</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white">
                                        {% for score in scores %}
                                        <tr>
                                            <td class="border border-gray-400 px-2 py-1 font-medium">{{ score.subject.name }}</td>
                                            {% for field in school.grading_fields.all|dictsort:"order" %}
                                            {% if field.is_active %}
                                            <td class="border border-gray-400 px-2 py-1 text-center">
                                                {{ score.scores_data|get_field_score:field.code|format_score }}
                                            </td>
                                            {% endif %}
                                            {% endfor %}
                                            <td class="border border-gray-400 px-2 py-1 text-center font-bold">
                                                {{ score.total_score|floatformat:1 }}
                                            </td>
                                            <td class="border border-gray-400 px-2 py-1 text-center font-bold">
                                                {{ score.grade }}
                                            </td>
                                            <td class="border border-gray-400 px-2 py-1 text-center font-bold">
                                                {{ score.position|default:"-" }}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Compact Comments and Signatures Section -->
                        <div class="mt-3">
                            <!-- Comments -->
                            <div class="grid grid-cols-2 gap-3 mb-3">
                                <div>
                                    <div class="text-xs font-bold mb-1">CLASS TEACHER'S COMMENT:</div>
                                    <div class="border border-gray-400 p-2 min-h-[40px] text-xs">
                                        {{ term_result.class_teacher_comment|default:"Good performance. Keep it up!" }}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-xs font-bold mb-1">HEAD TEACHER'S COMMENT:</div>
                                    <div class="border border-gray-400 p-2 min-h-[40px] text-xs">
                                        {{ term_result.head_teacher_comment|default:"Excellent work. Continue to strive for excellence." }}
                                    </div>
                                </div>
                            </div>

                            <!-- Signatures -->
                            <div class="grid grid-cols-3 gap-4 mt-4 text-xs">
                                <div class="text-center">
                                    <div class="border-t border-black mt-8 pt-1">
                                        <div class="font-bold">CLASS TEACHER'S SIGNATURE</div>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="border-t border-black mt-8 pt-1">
                                        <div class="font-bold">HEAD TEACHER'S SIGNATURE</div>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <div class="border-t border-black mt-8 pt-1">
                                        <div class="font-bold">PARENT'S SIGNATURE</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize Lucide icons
    lucide.createIcons();
</script>

<style>
    @media print {
        .no-print, .print-hide {
            display: none !important;
        }

        body {
            background: white !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 12px !important;
        }

        .bg-gray-50 {
            background: white !important;
        }

        .shadow-sm, .shadow-lg {
            box-shadow: none !important;
        }

        /* Ensure single A4 page */
        .report-container {
            page-break-inside: avoid;
            max-height: 100vh;
            overflow: hidden;
        }

        /* Compact spacing */
        .p-6, .p-8 {
            padding: 8px !important;
        }

        .py-8 {
            padding-top: 6px !important;
            padding-bottom: 6px !important;
        }

        .mb-8 {
            margin-bottom: 6px !important;
        }

        .gap-8 {
            gap: 6px !important;
        }

        /* Compact text sizes */
        h1 {
            font-size: 16px !important;
        }

        h2 {
            font-size: 14px !important;
        }

        h3 {
            font-size: 12px !important;
        }

        .text-3xl {
            font-size: 16px !important;
        }

        .text-2xl {
            font-size: 14px !important;
        }

        .text-xl {
            font-size: 12px !important;
        }

        .text-lg {
            font-size: 11px !important;
        }

        /* Compact table */
        table {
            width: 100% !important;
            font-size: 10px !important;
        }

        th, td {
            padding: 3px 4px !important;
            line-height: 1.2 !important;
        }

        /* Compact borders */
        .border {
            border-width: 1px !important;
        }

        /* Ensure content fits */
        .max-w-6xl {
            max-width: 100% !important;
            margin: 0 !important;
        }

        .grid {
            display: block !important;
        }

        .lg\\:col-span-2 {
            width: 100% !important;
        }

        .lg\\:col-span-1 {
            width: 100% !important;
        }
    }
</style>
{% endblock %}
