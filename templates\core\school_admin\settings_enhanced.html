{% extends 'base.html' %}
{% load static %}

{% block title %}School Settings - {{ school.name }}{% endblock %}

{% block content %}
<div class="p-6 space-y-8 animate-fade-in">
    <!-- Enhanced Header Section -->
    <div class="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl shadow-2xl">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/90 to-pink-600/90"></div>
        
        <!-- Decorative Elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        
        <div class="relative px-8 py-12">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex items-center space-x-6">
                    <!-- Settings Icon -->
                    <div class="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30">
                        <i data-lucide="settings" class="w-10 h-10 text-white"></i>
                    </div>
                    
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-2">School Settings</h1>
                        <p class="text-indigo-100 text-lg">Configure your school profile and system settings</p>
                        <div class="flex items-center mt-3 space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-indigo-100 text-sm">System configuration</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 lg:mt-0">
                    <a href="{% url 'admin_dashboard' %}"
                       class="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="bg-white rounded-3xl shadow-2xl border border-slate-200/50 overflow-hidden">
        <form method="post" enctype="multipart/form-data" id="settingsForm">
            {% csrf_token %}
            
            <!-- Tab Navigation -->
            <div class="border-b border-slate-200">
                <nav class="flex space-x-8 px-8" aria-label="Tabs">
                    <button type="button" class="tab-button active py-4 px-1 border-b-2 border-purple-500 font-medium text-sm text-purple-600" data-tab="school">
                        <i data-lucide="school" class="w-4 h-4 inline mr-2"></i>
                        School Profile
                    </button>
                    <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-slate-500 hover:text-slate-700 hover:border-slate-300" data-tab="email">
                        <i data-lucide="mail" class="w-4 h-4 inline mr-2"></i>
                        Email Notifications
                    </button>
                    <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-slate-500 hover:text-slate-700 hover:border-slate-300" data-tab="academic">
                        <i data-lucide="graduation-cap" class="w-4 h-4 inline mr-2"></i>
                        Academic Settings
                    </button>
                    <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-slate-500 hover:text-slate-700 hover:border-slate-300" data-tab="notifications">
                        <i data-lucide="bell" class="w-4 h-4 inline mr-2"></i>
                        Notifications
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-8">
                <!-- School Profile Tab -->
                <div id="school-tab" class="tab-content">
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 mb-6">
                            <div class="w-10 h-10 bg-purple-100 rounded-2xl flex items-center justify-center">
                                <i data-lucide="school" class="w-5 h-5 text-purple-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900">School Information</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- School Name -->
                            <div>
                                <label for="{{ form.name.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="building" class="w-4 h-4 inline mr-2 text-purple-500"></i>
                                    School Name *
                                </label>
                                {{ form.name }}
                                <p class="mt-2 text-xs text-slate-500">Official name of your school</p>
                            </div>

                            <!-- School Motto -->
                            <div>
                                <label for="{{ form.motto.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="quote" class="w-4 h-4 inline mr-2 text-blue-500"></i>
                                    School Motto
                                </label>
                                {{ form.motto }}
                                <p class="mt-2 text-xs text-slate-500">Your school's motto or slogan</p>
                            </div>

                            <!-- Phone Number -->
                            <div>
                                <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="phone" class="w-4 h-4 inline mr-2 text-green-500"></i>
                                    Phone Number
                                </label>
                                {{ form.phone_number }}
                                <p class="mt-2 text-xs text-slate-500">Main contact number</p>
                            </div>

                            <!-- Location -->
                            <div>
                                <label for="{{ form.location.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="map-pin" class="w-4 h-4 inline mr-2 text-red-500"></i>
                                    Location
                                </label>
                                {{ form.location }}
                                <p class="mt-2 text-xs text-slate-500">City, Region</p>
                            </div>

                            <!-- Academic Year -->
                            <div>
                                <label for="{{ form.academic_year.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="calendar" class="w-4 h-4 inline mr-2 text-orange-500"></i>
                                    Academic Year *
                                </label>
                                {{ form.academic_year }}
                                <p class="mt-2 text-xs text-slate-500">Current academic year</p>
                            </div>

                            <!-- Current Term -->
                            <div>
                                <label for="{{ form.current_term.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="clock" class="w-4 h-4 inline mr-2 text-indigo-500"></i>
                                    Current Term *
                                </label>
                                {{ form.current_term }}
                                <p class="mt-2 text-xs text-slate-500">Active academic term</p>
                            </div>
                        </div>

                        <!-- Address -->
                        <div>
                            <label for="{{ form.address.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                <i data-lucide="map" class="w-4 h-4 inline mr-2 text-purple-500"></i>
                                School Address
                            </label>
                            {{ form.address }}
                            <p class="mt-2 text-xs text-slate-500">Complete postal address</p>
                        </div>

                        <!-- Logo Upload -->
                        <div>
                            <label for="{{ form.logo.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                <i data-lucide="image" class="w-4 h-4 inline mr-2 text-pink-500"></i>
                                School Logo
                            </label>
                            {{ form.logo }}
                            <p class="mt-2 text-xs text-slate-500">Upload your school logo (recommended: 200x200px)</p>
                            {% if school.logo %}
                            <div class="mt-3">
                                <img src="{{ school.logo.url }}" alt="Current Logo" class="w-16 h-16 object-contain border border-slate-200 rounded-lg">
                                <p class="text-xs text-slate-500 mt-1">Current logo</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Email Notifications Tab -->
                <div id="email-tab" class="tab-content hidden">
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 mb-6">
                            <div class="w-10 h-10 bg-blue-100 rounded-2xl flex items-center justify-center">
                                <i data-lucide="mail" class="w-5 h-5 text-blue-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900">Email Notifications</h3>
                        </div>

                        <div class="bg-blue-50 border-2 border-blue-200/50 rounded-2xl p-6 mb-6">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center flex-shrink-0">
                                    <i data-lucide="info" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-bold text-blue-900 mb-3">Email Notification Settings</h4>
                                    <div class="space-y-2 text-sm text-blue-800">
                                        <p>Control which email notifications your school receives. Email infrastructure is managed by the site administrator.</p>
                                        <p class="font-medium">Available notifications:</p>
                                        <ul class="list-disc list-inside ml-4 space-y-1">
                                            <li>Welcome emails for new teachers (with {{ school.name }} branding)</li>
                                            <li>Password reset notifications</li>
                                            <li>Grade submission alerts</li>
                                            <li>System notifications</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <!-- Welcome Emails -->
                            <div class="bg-white border-2 border-slate-200 rounded-2xl p-6">
                                <div class="flex items-center space-x-3 mb-4">
                                    {{ form.send_welcome_emails }}
                                    <label for="{{ form.send_welcome_emails.id_for_label }}" class="text-sm font-semibold text-slate-700">
                                        <i data-lucide="user-plus" class="w-4 h-4 inline mr-2 text-green-500"></i>
                                        Send Welcome Emails to New Teachers
                                    </label>
                                </div>
                                <p class="text-xs text-slate-500 ml-8">Automatically send welcome emails with login credentials when you add new teachers</p>
                            </div>

                            <!-- Password Reset Emails -->
                            <div class="bg-white border-2 border-slate-200 rounded-2xl p-6">
                                <div class="flex items-center space-x-3 mb-4">
                                    {{ form.send_password_reset_emails }}
                                    <label for="{{ form.send_password_reset_emails.id_for_label }}" class="text-sm font-semibold text-slate-700">
                                        <i data-lucide="key" class="w-4 h-4 inline mr-2 text-blue-500"></i>
                                        Send Password Reset Emails
                                    </label>
                                </div>
                                <p class="text-xs text-slate-500 ml-8">Send email notifications when you reset teacher passwords</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Academic Settings Tab -->
                <div id="academic-tab" class="tab-content hidden">
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 mb-6">
                            <div class="w-10 h-10 bg-green-100 rounded-2xl flex items-center justify-center">
                                <i data-lucide="graduation-cap" class="w-5 h-5 text-green-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900">Academic Configuration</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Grading System -->
                            <div>
                                <label for="{{ form.grading_system.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="award" class="w-4 h-4 inline mr-2 text-yellow-500"></i>
                                    Grading System
                                </label>
                                {{ form.grading_system }}
                                <p class="mt-2 text-xs text-slate-500">Choose your preferred grading system</p>
                            </div>

                            <!-- Report Card Template -->
                            <div>
                                <label for="{{ form.report_template.id_for_label }}" class="block text-sm font-semibold text-slate-700 mb-3">
                                    <i data-lucide="layout-template" class="w-4 h-4 inline mr-2 text-purple-500"></i>
                                    Report Card Template
                                </label>
                                {{ form.report_template }}
                                <p class="mt-2 text-xs text-slate-500">Choose your preferred report card design</p>

                                <!-- Template Preview -->
                                <div id="template-preview" class="mt-4 hidden">
                                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                        <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                            <i data-lucide="eye" class="w-4 h-4 mr-2 text-purple-500"></i>
                                            Template Preview
                                        </h4>

                                        <!-- Visual Preview Container -->
                                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                            <!-- Template Info -->
                                            <div id="preview-content" class="text-sm text-gray-600">
                                                Select a template to see preview
                                            </div>

                                            <!-- Mini Visual Preview -->
                                            <div id="visual-preview" class="hidden">
                                                <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm">
                                                    <div class="text-xs text-gray-500 mb-2 text-center">Visual Preview</div>
                                                    <div id="mini-report-card" class="bg-white border rounded text-xs" style="width: 200px; height: 280px; margin: 0 auto;">
                                                        <!-- Mini report card will be generated here -->
                                                    </div>
                                                    <div class="mt-2 text-center">
                                                        <button type="button" id="full-preview-btn" class="text-xs text-purple-600 hover:text-purple-700 underline">
                                                            View Full Preview
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div id="notifications-tab" class="tab-content hidden">
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 mb-6">
                            <div class="w-10 h-10 bg-orange-100 rounded-2xl flex items-center justify-center">
                                <i data-lucide="bell" class="w-5 h-5 text-orange-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-slate-900">Notification Settings</h3>
                        </div>

                        <div class="space-y-4">
                            <!-- Welcome Emails -->
                            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-2xl">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="user-plus" class="w-5 h-5 text-green-500"></i>
                                    <div>
                                        <h4 class="font-semibold text-slate-900">Welcome Emails</h4>
                                        <p class="text-sm text-slate-600">Send welcome emails to new teachers</p>
                                    </div>
                                </div>
                                {{ form.send_welcome_emails }}
                            </div>

                            <!-- Password Reset Emails -->
                            <div class="flex items-center justify-between p-4 bg-slate-50 rounded-2xl">
                                <div class="flex items-center space-x-3">
                                    <i data-lucide="key" class="w-5 h-5 text-red-500"></i>
                                    <div>
                                        <h4 class="font-semibold text-slate-900">Password Reset Emails</h4>
                                        <p class="text-sm text-slate-600">Send emails when passwords are reset</p>
                                    </div>
                                </div>
                                {{ form.send_password_reset_emails }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="border-t border-slate-200 px-8 py-6 bg-slate-50">
                <div class="flex flex-col sm:flex-row gap-4 sm:justify-end">
                    <button type="button" id="testEmailBtn" class="inline-flex items-center justify-center px-6 py-3 border-2 border-blue-300 rounded-2xl text-sm font-semibold text-blue-700 bg-blue-50 hover:bg-blue-100 hover:border-blue-400 transition-all duration-300">
                        <i data-lucide="send" class="w-5 h-5 mr-2"></i>
                        Test Email Configuration
                    </button>
                    <button type="submit" class="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm font-semibold rounded-2xl hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                        <i data-lucide="save" class="w-5 h-5 mr-2"></i>
                        Save Settings
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    // Initialize Lucide icons and tab functionality
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
        
        // Tab functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'border-purple-500', 'text-purple-600');
                    btn.classList.add('border-transparent', 'text-slate-500');
                });
                
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Add active class to clicked button
                this.classList.add('active', 'border-purple-500', 'text-purple-600');
                this.classList.remove('border-transparent', 'text-slate-500');
                
                // Show corresponding content
                document.getElementById(tabName + '-tab').classList.remove('hidden');
            });
        });
        
        // Template preview functionality
        const templatePreviews = {
            '1': {
                name: 'Classic Professional Blue',
                description: 'Traditional professional design with blue color scheme, perfect for formal academic institutions',
                features: ['School logo integration', 'Student photo placeholder', 'Professional header', 'Formal border design', 'Traditional layout']
            },
            '2': {
                name: 'Modern Minimalist Green',
                description: 'Clean, modern design with green accents, ideal for contemporary schools',
                features: ['Minimalist design', 'Clean typography', 'Green color scheme', 'Modern layout', 'Simplified structure']
            },
            '3': {
                name: 'Royal Purple Elegant',
                description: 'Elegant design with royal purple theme, suitable for prestigious institutions',
                features: ['Elegant styling', 'Royal purple theme', 'Decorative elements', 'Premium appearance', 'Sophisticated layout']
            },
            '4': {
                name: 'Vibrant Orange Creative',
                description: 'Creative and colorful design with orange theme, perfect for creative arts schools',
                features: ['Creative design', 'Vibrant colors', 'Artistic elements', 'Dynamic layout', 'Engaging appearance']
            },
            '5': {
                name: 'Professional Teal Modern',
                description: 'Modern professional design with teal accents, great for tech-focused schools',
                features: ['Modern styling', 'Tech-inspired design', 'Teal color scheme', 'Professional layout', 'Contemporary feel']
            },
            '6': {
                name: 'Sophisticated Gray Classic',
                description: 'Sophisticated gray design with classic elements, suitable for formal institutions',
                features: ['Sophisticated design', 'Classic elements', 'Gray color scheme', 'Formal appearance', 'Traditional structure']
            }
        };

        // Handle template selection change
        const templateSelect = document.getElementById('{{ form.report_template.id_for_label }}');
        const previewDiv = document.getElementById('template-preview');
        const previewContent = document.getElementById('preview-content');
        const visualPreview = document.getElementById('visual-preview');
        const miniReportCard = document.getElementById('mini-report-card');

        if (templateSelect) {
            templateSelect.addEventListener('change', function() {
                const selectedValue = this.value;

                if (selectedValue) {
                    // Show loading state
                    previewDiv.classList.remove('hidden');
                    previewContent.innerHTML = '<div class="flex items-center space-x-2"><div class="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div><span>Loading preview...</span></div>';
                    visualPreview.classList.add('hidden');

                    // Fetch template preview data
                    fetch(`/school-admin/preview-template/${selectedValue}/`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Update preview content
                        let featuresHtml = '';
                        if (data.features && data.features.length > 0) {
                            featuresHtml = '<ul class="list-disc list-inside space-y-1 mt-2">';
                            data.features.forEach(feature => {
                                featuresHtml += `<li class="text-xs text-gray-600">${feature}</li>`;
                            });
                            featuresHtml += '</ul>';
                        }

                        previewContent.innerHTML = `
                            <div class="space-y-3">
                                <div>
                                    <h5 class="font-semibold text-gray-900">${data.name}</h5>
                                    <p class="text-sm text-gray-600 mt-1">${data.description || 'No description available'}</p>
                                </div>
                                <div class="grid grid-cols-1 gap-2 text-xs">
                                    <div>
                                        <span class="font-medium text-gray-700">Type:</span>
                                        <span class="text-gray-600">${data.template_type}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">Style:</span>
                                        <span class="text-gray-600">${data.design_style}</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-gray-700">Color:</span>
                                        <span class="text-gray-600">${data.color_scheme}</span>
                                    </div>
                                </div>
                                ${featuresHtml}
                            </div>
                        `;

                        // Generate visual preview
                        generateMiniReportCard(data);
                        visualPreview.classList.remove('hidden');

                        // Add full preview button functionality
                        const fullPreviewBtn = document.getElementById('full-preview-btn');
                        if (fullPreviewBtn) {
                            fullPreviewBtn.onclick = function() {
                                window.open(`/school-admin/report-templates/${selectedValue}/preview/`, '_blank');
                            };
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching template preview:', error);
                        previewContent.innerHTML = '<p class="text-red-600 text-sm">Error loading preview</p>';
                        visualPreview.classList.add('hidden');
                    });
                } else {
                    previewDiv.classList.add('hidden');
                    visualPreview.classList.add('hidden');
                }

                if (selectedValue && templatePreviews[selectedValue]) {
                    const template = templatePreviews[selectedValue];

                    previewContent.innerHTML = `
                        <div class="space-y-3">
                            <div>
                                <h5 class="font-medium text-gray-900">${template.name}</h5>
                                <p class="text-sm text-gray-600 mt-1">${template.description}</p>
                            </div>
                            <div>
                                <h6 class="text-sm font-medium text-gray-900 mb-2">Features:</h6>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    ${template.features.map(feature => `<li class="flex items-center"><i data-lucide="check" class="w-3 h-3 mr-2 text-green-500"></i>${feature}</li>`).join('')}
                                </ul>
                            </div>
                        </div>
                    `;

                    previewDiv.classList.remove('hidden');
                    lucide.createIcons();
                } else {
                    previewDiv.classList.add('hidden');
                }
            });

            // Trigger change event if there's already a selected value
            if (templateSelect.value) {
                templateSelect.dispatchEvent(new Event('change'));
            }
        }

        // Function to generate mini report card preview
        function generateMiniReportCard(data) {
            const colorScheme = data.color_scheme.toLowerCase();
            const designStyle = data.design_style.toLowerCase();

            // Color mapping for different schemes
            const colorMap = {
                'blue': { primary: '#2563eb', secondary: '#dbeafe', text: '#1e40af' },
                'green': { primary: '#059669', secondary: '#d1fae5', text: '#047857' },
                'purple': { primary: '#7c3aed', secondary: '#e9d5ff', text: '#6b21a8' },
                'orange': { primary: '#ea580c', secondary: '#fed7aa', text: '#c2410c' },
                'teal': { primary: '#0d9488', secondary: '#ccfbf1', text: '#0f766e' },
                'gray': { primary: '#4b5563', secondary: '#f3f4f6', text: '#374151' }
            };

            const colors = colorMap[colorScheme] || colorMap['blue'];

            let headerStyle = '';
            let contentStyle = '';

            if (designStyle === 'classic') {
                headerStyle = `background: ${colors.primary}; color: white; padding: 8px; text-align: center; border-radius: 4px 4px 0 0;`;
                contentStyle = `border: 2px solid ${colors.primary}; border-top: none;`;
            } else if (designStyle === 'modern') {
                headerStyle = `background: linear-gradient(135deg, ${colors.primary}, ${colors.text}); color: white; padding: 8px; text-align: center; border-radius: 6px 6px 0 0;`;
                contentStyle = `border: 1px solid ${colors.secondary}; border-top: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1);`;
            } else {
                headerStyle = `background: ${colors.secondary}; color: ${colors.text}; padding: 8px; text-align: center; border-radius: 4px 4px 0 0; border: 1px solid ${colors.primary};`;
                contentStyle = `border: 1px solid ${colors.primary}; border-top: none;`;
            }

            const miniReportHtml = `
                <div style="${headerStyle}">
                    <div style="font-size: 10px; font-weight: bold; margin-bottom: 2px;">SAMPLE SCHOOL</div>
                    <div style="font-size: 8px;">TERMINAL REPORT</div>
                </div>
                <div style="${contentStyle} padding: 6px; height: 240px; overflow: hidden;">
                    <div style="font-size: 8px; margin-bottom: 4px;">
                        <strong>Name:</strong> John Doe<br>
                        <strong>Class:</strong> Basic 6A<br>
                        <strong>Term:</strong> First Term
                    </div>

                    <div style="margin: 6px 0; border-top: 1px solid ${colors.secondary}; padding-top: 4px;">
                        <div style="font-size: 7px; font-weight: bold; margin-bottom: 3px;">SUBJECTS</div>
                        <div style="font-size: 6px; line-height: 1.2;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 1px;">
                                <span>English</span><span>85 A</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 1px;">
                                <span>Math</span><span>78 B</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 1px;">
                                <span>Science</span><span>82 A</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 1px;">
                                <span>Social St.</span><span>75 B</span>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 6px; border-top: 1px solid ${colors.secondary}; padding-top: 4px;">
                        <div style="font-size: 6px; text-align: center;">
                            <div><strong>Position:</strong> 4th of 25</div>
                            <div><strong>Average:</strong> 80.0%</div>
                        </div>
                    </div>

                    <div style="margin-top: 6px; font-size: 6px; text-align: center; color: ${colors.text};">
                        <div style="border-top: 1px solid ${colors.secondary}; padding-top: 4px;">
                            <strong>Remarks:</strong> Excellent work!
                        </div>
                    </div>
                </div>
            `;

            miniReportCard.innerHTML = miniReportHtml;
        }

        // Test email functionality
        const testEmailBtn = document.getElementById('testEmailBtn');
        if (testEmailBtn) {
            testEmailBtn.addEventListener('click', function() {
                // Show loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<i data-lucide="loader" class="w-4 h-4 inline mr-2 animate-spin"></i>Sending Test Email...';
                this.disabled = true;

                // Get test email address (use current user's email as default)
                const testEmail = prompt('Enter email address to send test email to:', '{{ user.email }}');

                if (testEmail) {
                    // Send AJAX request to test email endpoint
                    fetch('{% url "test_email_configuration" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: 'test_email=' + encodeURIComponent(testEmail)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('✅ ' + data.message);
                        } else {
                            alert('❌ ' + data.message + (data.error ? '\n\nError: ' + data.error : ''));
                        }
                    })
                    .catch(error => {
                        alert('❌ Error testing email: ' + error.message);
                    })
                    .finally(() => {
                        // Restore button state
                        this.innerHTML = originalText;
                        this.disabled = false;
                        // Re-initialize Lucide icons
                        if (typeof lucide !== 'undefined') {
                            lucide.createIcons();
                        }
                    });
                } else {
                    // Restore button state if cancelled
                    this.innerHTML = originalText;
                    this.disabled = false;
                }
            });
        }
        
        // Form field focus effects
        const formInputs = document.querySelectorAll('input, select, textarea');
        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.add('ring-2', 'ring-purple-500', 'ring-opacity-50');
            });
            
            input.addEventListener('blur', function() {
                this.classList.remove('ring-2', 'ring-purple-500', 'ring-opacity-50');
            });
        });
    });
</script>
{% endblock %}
