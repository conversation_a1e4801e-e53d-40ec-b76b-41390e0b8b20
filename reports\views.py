from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.template.loader import render_to_string
from core.models import *
from django.contrib import messages
import zipfile
from io import BytesIO
import os
from datetime import datetime
import base64
from django.conf import settings

# Professional PDF generation with xhtml2pdf (Windows-friendly)
try:
    from xhtml2pdf import pisa
    from django.template.loader import get_template
    XHTML2PDF_AVAILABLE = True
except ImportError:
    XHTML2PDF_AVAILABLE = False

def get_school_logo_base64(school):
    """Get school logo as base64 string for embedding in HTML"""
    try:
        # Check if school has a logo field and file exists
        if hasattr(school, 'logo') and school.logo:
            logo_path = school.logo.path
        else:
            # Try common logo file names in media/school_logos/
            logo_dir = os.path.join(settings.MEDIA_ROOT, 'school_logos')
            possible_names = ['dgm_logo.png', 'logo.png', f'{school.name.lower().replace(" ", "_")}_logo.png']

            logo_path = None
            for name in possible_names:
                test_path = os.path.join(logo_dir, name)
                if os.path.exists(test_path):
                    logo_path = test_path
                    break

        if logo_path and os.path.exists(logo_path):
            with open(logo_path, 'rb') as img_file:
                return base64.b64encode(img_file.read()).decode('utf-8')
    except Exception as e:
        print(f"Error loading school logo: {e}")

    return None

# Fallback to reportlab
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

def generate_student_report_pdf_professional(student, result, scores, school):
    """Generate a professional PDF report using xhtml2pdf (HTML to PDF)"""
    if XHTML2PDF_AVAILABLE:
        # Prepare context data similar to teacher preview
        from core.models import SchoolGradingField, Subject, Score

        # Get current term and academic year
        current_term = school.current_term
        academic_year = school.academic_year

        # Convert student profile picture to base64 for PDF embedding
        student_photo_base64 = None
        if student.profile_picture:
            try:
                import base64
                from django.conf import settings
                import os

                # Get the full file path
                photo_path = os.path.join(settings.MEDIA_ROOT, student.profile_picture.name)
                if os.path.exists(photo_path):
                    with open(photo_path, 'rb') as image_file:
                        image_data = image_file.read()
                        student_photo_base64 = base64.b64encode(image_data).decode('utf-8')
            except Exception as e:
                print(f"Error converting student photo to base64: {e}")
                student_photo_base64 = None

        # Convert school logo to base64 for PDF embedding
        school_logo_base64 = None
        if school.logo:
            try:
                import base64
                from django.conf import settings
                import os

                # Get the full file path
                logo_path = os.path.join(settings.MEDIA_ROOT, school.logo.name)
                if os.path.exists(logo_path):
                    with open(logo_path, 'rb') as image_file:
                        image_data = image_file.read()
                        school_logo_base64 = base64.b64encode(image_data).decode('utf-8')
            except Exception as e:
                print(f"Error converting school logo to base64: {e}")
                school_logo_base64 = None

        # Get school's grading fields
        grading_fields = school.grading_fields.filter(is_active=True).order_by('order', 'name')

        # Get all subjects for this class through ClassSubject relationship
        from core.models import ClassSubject
        class_subjects = ClassSubject.objects.filter(
            class_name=student.current_class,
            is_active=True
        ).select_related('subject')
        all_class_subjects = [cs.subject for cs in class_subjects if cs.subject.is_active]

        # Prepare subject data similar to teacher preview
        subject_data = []
        total_score = 0
        subjects_with_scores = 0

        for subject in all_class_subjects:
            try:
                score = Score.objects.get(
                    student=student,
                    subject=subject,
                    term=current_term,
                    academic_year=academic_year,
                    is_submitted=True
                )
                subject_data.append({
                    'subject': subject,
                    'score': score,
                    'has_data': True,
                    'total_score': score.total_score,
                    'grade': score.grade,
                    'position': getattr(score, 'position', 0)
                })
                total_score += score.total_score
                subjects_with_scores += 1
            except Score.DoesNotExist:
                subject_data.append({
                    'subject': subject,
                    'score': None,
                    'has_data': False,
                    'total_score': 0,
                    'grade': '-',
                    'position': 0
                })

        # Calculate averages and position
        average_score = total_score / subjects_with_scores if subjects_with_scores > 0 else 0

        # Get class position if result exists
        class_position = result.position if result else 0

        # Get total students in class
        from core.models import Student
        total_students = Student.objects.filter(
            current_class=student.current_class,
            is_active=True
        ).count()

        # Get school logo as base64 for embedding
        school_logo_base64 = get_school_logo_base64(school)

        # Get next term reopening date from school settings
        next_term_date = school.next_term_reopening_date.strftime("%B %d, %y").upper() if school.next_term_reopening_date else "TBA"

        # Format attendance
        attendance_display = f"{result.attendance_present} OUT OF {result.attendance_total}" if result.attendance_total > 0 else "Not Set"

        # Generate document security hash for verification
        import hashlib
        from datetime import datetime
        security_string = f"{school.id}-{student.id}-{current_term}-{academic_year}-{total_score}-{datetime.now().strftime('%Y%m%d')}"
        document_hash = hashlib.md5(security_string.encode()).hexdigest()[:8].upper()

        context = {
            'student': student,
            'result': result,
            'scores': scores,
            'school': school,
            'school_logo_base64': school_logo_base64,
            'student_photo_base64': student_photo_base64,
            'subject_data': subject_data,
            'grading_fields': grading_fields,
            'current_term': current_term,
            'academic_year': academic_year,
            'total_score': total_score,
            'average_score': average_score,
            'class_position': class_position,
            'total_students': total_students,
            'subjects_with_scores': subjects_with_scores,
            # Dynamic values from StudentTermResult
            'attendance': attendance_display,
            'promoted_to': result.promoted_to or '',
            'conduct': result.conduct_comment or '',
            'attitude': result.attitude_comment or '',
            'interest': result.interest_comment or '',
            'teacher_comment': result.teacher_comment or '',
            'next_term_date': next_term_date,
            # Security features
            'document_hash': document_hash,
        }

        # Use the unified template
        from django.template.loader import render_to_string

        html_content = render_to_string('reports/professional_report_pdf.html', context)

        # Generate PDF using xhtml2pdf
        result_file = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html_content.encode("UTF-8")), result_file)

        if not pdf.err:
            result_file.seek(0)
            return result_file.getvalue()
        else:
            # If xhtml2pdf fails, fallback to reportlab
            return generate_student_report_pdf_fallback(student, result, scores, school)
    else:
        # Fallback to old method if xhtml2pdf not available
        return generate_student_report_pdf_fallback(student, result, scores, school)

def generate_student_report_pdf_fallback(student, result, scores, school):
    """Generate a PDF report for a student matching the HTML preview exactly"""
    buffer = BytesIO()
    # Use appropriate margins
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=40, leftMargin=40, topMargin=40, bottomMargin=40)

    # Container for the 'Flowable' objects
    elements = []

    # Define colors - matching the preview exactly
    primary_color = colors.Color(0.145, 0.251, 0.918)  # Professional Blue
    light_gray = colors.Color(0.9, 0.9, 0.9)

    # Define styles
    styles = getSampleStyleSheet()

    # Header Section - exactly matching the preview layout
    # Create a simple rectangle for logo placeholder
    from reportlab.graphics.shapes import Drawing, Rect
    from reportlab.graphics import renderPDF

    # Create the main header table with logo, school info, and student photo
    header_data = [
        [
            # Logo cell - create a bordered box like the preview
            Table([['SCHOOL'], ['LOGO']], colWidths=[1.2*inch], rowHeights=[0.3*inch, 0.3*inch]),

            # School info cell - using actual school data with proper formatting
            [
                Paragraph(f'<para align="center" spaceAfter="4"><b>{school.name.upper()}</b></para>',
                         ParagraphStyle('SchoolName', parent=styles['Normal'], fontSize=16,
                                      alignment=TA_CENTER, textColor=primary_color, fontName='Helvetica-Bold')),
                Paragraph('<para align="center" spaceAfter="2">SCHOOL COMPLEX</para>',
                         ParagraphStyle('SchoolComplex', parent=styles['Normal'], fontSize=11,
                                      alignment=TA_CENTER)),
                Paragraph(f'<para align="center" spaceAfter="1">{school.address or "Near Kas Valley Properties"}</para>',
                         ParagraphStyle('Address', parent=styles['Normal'], fontSize=10, alignment=TA_CENTER)),
                Paragraph(f'<para align="center" spaceAfter="1">{school.phone_number or "0541889697"}</para>',
                         ParagraphStyle('Phone', parent=styles['Normal'], fontSize=10, alignment=TA_CENTER)),
                Paragraph(f'<para align="center">{school.admin.email or "<EMAIL>"}</para>',
                         ParagraphStyle('Email', parent=styles['Normal'], fontSize=10, alignment=TA_CENTER))
            ],

            # Student photo cell - create a bordered box like the preview
            Table([['STUDENT'], ['PHOTO']], colWidths=[1.2*inch], rowHeights=[0.3*inch, 0.3*inch])
        ]
    ]

    # Style the logo and photo boxes
    logo_table = header_data[0][0]
    logo_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))

    photo_table = header_data[0][2]
    photo_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))

    header_table = Table(header_data, colWidths=[1.4*inch, 4.2*inch, 1.4*inch])
    header_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOX', (0, 0), (-1, -1), 2, primary_color),
        ('GRID', (0, 0), (-1, -1), 1, primary_color),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
        ('TOPPADDING', (0, 0), (-1, -1), 15),
    ]))
    elements.append(header_table)
    elements.append(Spacer(1, 8))

    # Terminal Report Sheet header - blue background like preview
    terminal_header = Paragraph('<para align="center"><b>TERMINAL REPORT SHEET</b></para>',
                               ParagraphStyle('TerminalHeader', parent=styles['Normal'], fontSize=14,
                                            alignment=TA_CENTER, textColor=colors.white, fontName='Helvetica-Bold'))
    terminal_table = Table([[terminal_header]], colWidths=[7*inch])
    terminal_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), primary_color),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('TOPPADDING', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
    ]))
    elements.append(terminal_table)
    elements.append(Spacer(1, 12))

    # Student Information Section - exactly matching the preview with proper spacing
    info_data = [
        ['Student ID:', student.student_id or 'STU139', 'Academic Year:', f'{school.academic_year}'],
        ['Name:', student.full_name.upper(), 'Academic Term:', f'{school.current_term}'],
        ['Class:', student.current_class.name if student.current_class else 'BASIC 1', 'School reopens:', 'JUNE 05, 19']
    ]

    info_table = Table(info_data, colWidths=[1.3*inch, 2.2*inch, 1.3*inch, 2.2*inch])
    info_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # First column labels
        ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),  # Third column labels
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),       # Second column values
        ('FONTNAME', (3, 0), (3, -1), 'Helvetica'),       # Fourth column values
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 6),
    ]))
    elements.append(info_table)
    elements.append(Spacer(1, 12))

    # Academic Performance Table - using actual student data
    if scores:
        # Get school's grading fields for dynamic column headers
        grading_fields = school.grading_fields.filter(is_active=True).order_by('order')

        # Create header structure dynamically based on grading fields
        grading_fields_list = list(grading_fields)

        # First row - build dynamically
        header_row1 = ['SUBJECT']
        # Add "CLASS SCORE" spanning all grading fields
        header_row1.extend(['CLASS SCORE'] + [''] * (len(grading_fields_list) - 1))
        header_row1.extend(['TOTAL\nSCORE', 'FINAL\nSCORE', 'GRADE', 'POS', 'REMARKS'])

        # Second row - build dynamically
        header_row2 = ['']
        # Add grading field headers
        for field in grading_fields_list:
            header_row2.append(f'{field.name.upper()}\n{int(field.percentage)}%')
        # Add total column for converted scores
        header_row2.append('TOTAL\n100%')
        header_row2.extend(['', '', '', ''])

        score_data = [header_row1, header_row2]

        # Add actual student scores
        for score in scores:
            # Get converted scores using the same logic as the template filter
            from core.templatetags.score_filters import get_display_score

            # Get grading fields for this school
            grading_fields_list = list(grading_fields)

            # Get converted scores for each grading field
            field_scores = []
            for field in grading_fields_list:
                converted_score = get_display_score(score, field.code)
                field_scores.append(converted_score if converted_score else 0)

            # Calculate total of converted scores for display in "TOTAL" column
            total_converted = sum(field_scores) if field_scores else 0

            # Generate remarks based on grade
            if score.grade in ['A', 'A1', '1']:
                remarks = 'EXCELLENT'
            elif score.grade in ['B', 'B2', 'B3', '2', '3']:
                remarks = 'VERY GOOD'
            elif score.grade in ['C', 'C4', 'C5', 'C6', '4', '5', '6']:
                remarks = 'GOOD'
            elif score.grade in ['D', 'D7', '7']:
                remarks = 'PASS'
            else:
                remarks = 'WEAK'

            # Build row with converted scores
            row = [score.subject.name]

            # Add converted scores for each grading field
            for converted_score in field_scores:
                row.append(f'{int(converted_score)}' if converted_score else '-')

            # Add total converted score
            row.append(f'{int(total_converted)}' if total_converted else '-')

            # Add remaining columns
            row.extend([
                f'{int(score.total_score)}' if score.total_score else '-',  # Final calculated total
                score.grade or '-',
                str(score.position) if score.position else '-',
                remarks
            ])

            score_data.append(row)

        # Calculate column widths dynamically
        num_grading_fields = len(grading_fields_list)
        subject_width = 2.0
        grading_field_width = 0.8
        total_width = 0.7
        final_width = 0.7
        grade_width = 0.6
        pos_width = 0.4
        remarks_width = 1.0

        col_widths = [subject_width * inch]
        col_widths.extend([grading_field_width * inch] * num_grading_fields)
        col_widths.extend([total_width * inch, final_width * inch, grade_width * inch, pos_width * inch, remarks_width * inch])

        scores_table = Table(score_data, colWidths=col_widths)

        # Table styling with dynamic column spans
        table_style = [
            # Header styling - blue background for both header rows
            ('BACKGROUND', (0, 0), (-1, 1), primary_color),
            ('TEXTCOLOR', (0, 0), (-1, 1), colors.white),
            ('FONTNAME', (0, 0), (-1, 1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # Merge cells for "SUBJECT" (spans 2 rows)
            ('SPAN', (0, 0), (0, 1)),
            # Merge cells for "CLASS SCORE" (spans all grading fields)
            ('SPAN', (1, 0), (num_grading_fields, 0)),
            # Merge cells for "TOTAL SCORE" (spans 2 rows)
            ('SPAN', (num_grading_fields + 1, 0), (num_grading_fields + 1, 1)),
            # Merge cells for "FINAL SCORE" (spans 2 rows)
            ('SPAN', (num_grading_fields + 2, 0), (num_grading_fields + 2, 1)),
            # Merge cells for "GRADE" (spans 2 rows)
            ('SPAN', (num_grading_fields + 3, 0), (num_grading_fields + 3, 1)),
            # Merge cells for "POS" (spans 2 rows)
            ('SPAN', (num_grading_fields + 4, 0), (num_grading_fields + 4, 1)),
            # Merge cells for "REMARKS" (spans 2 rows)
            ('SPAN', (num_grading_fields + 5, 0), (num_grading_fields + 5, 1)),

            # Data styling - larger font and bold for scores
            ('FONTNAME', (0, 2), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 2), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),

            # Subject column left aligned and normal weight
            ('ALIGN', (0, 2), (0, -1), 'LEFT'),
            ('LEFTPADDING', (0, 2), (0, -1), 10),
            ('FONTNAME', (0, 2), (0, -1), 'Helvetica'),

            # Remarks column left aligned and normal weight
            ('ALIGN', (7, 2), (7, -1), 'LEFT'),
            ('LEFTPADDING', (7, 2), (7, -1), 10),
            ('FONTNAME', (7, 2), (7, -1), 'Helvetica'),

            # Borders - exactly like preview
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
        ]

        scores_table.setStyle(TableStyle(table_style))
        elements.append(scores_table)
        elements.append(Spacer(1, 15))

    # Summary Section - using actual student data
    if result:
        # Calculate total students in class
        total_students = StudentTermResult.objects.filter(
            student__current_class=student.current_class,
            term=school.current_term,
            academic_year=school.academic_year
        ).count()

        # Get position suffix
        position = result.position
        if position == 1:
            position_suffix = 'st'
        elif position == 2:
            position_suffix = 'nd'
        elif position == 3:
            position_suffix = 'rd'
        else:
            position_suffix = 'th'

        summary_row1 = [
            f'No On Roll:\n{total_students}',
            f'Total Marks:\n{result.total_score:.1f}',
            f'Average Mark:\n{result.average_score:.1f}'
        ]

        summary_row2 = [
            'Attendance:\n55 OUT OF 55',
            'Promoted to:\nBASIC 4',
            f'Position in Class:\n{position}{position_suffix}'
        ]
    else:
        # Fallback data if no result
        summary_row1 = [
            'No On Roll:\n44',
            'Total Marks:\n0.0',
            'Average Mark:\n0.0'
        ]

        summary_row2 = [
            'Attendance:\n55 OUT OF 55',
            'Promoted to:\nBASIC 4',
            'Position in Class:\n-'
        ]

    summary_data = [summary_row1, summary_row2]
    summary_table = Table(summary_data, colWidths=[2.33*inch, 2.33*inch, 2.34*inch])
    summary_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
        ('TOPPADDING', (0, 0), (-1, -1), 10),
    ]))
    elements.append(summary_table)
    elements.append(Spacer(1, 12))

    # Comments Section - using actual data if available (horizontal layout)
    conduct = getattr(result, 'conduct', 'Respectful and obedient.') if result else 'Respectful and obedient.'
    attitude = getattr(result, 'attitude', 'Hardworking') if result else 'Hardworking'

    comments_data = [
        ['Conduct:', conduct, 'Attitude:', attitude],
        ['Interest:', 'Good keen interest in studies', 'Class Teacher\'s Remarks:', 'Good performance, keep it up!']
    ]

    comments_table = Table(comments_data, colWidths=[1.2*inch, 2.3*inch, 1.2*inch, 2.3*inch])
    comments_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # First column labels
        ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),  # Third column labels
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),       # Second column values
        ('FONTNAME', (3, 0), (3, -1), 'Helvetica'),       # Fourth column values
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
    ]))
    elements.append(comments_table)

    # Add signature section at the bottom
    elements.append(Spacer(1, 20))

    signature_data = [
        ['CLASS TEACHER\'S SIGNATURE', 'HEAD TEACHER\'S SIGNATURE', 'PARENT\'S SIGNATURE']
    ]

    signature_table = Table(signature_data, colWidths=[2.33*inch, 2.33*inch, 2.34*inch])
    signature_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
        ('TOPPADDING', (0, 0), (-1, -1), 15),
    ]))
    elements.append(signature_table)

    # Build PDF
    doc.build(elements)
    buffer.seek(0)
    return buffer.getvalue()

@login_required
def reports_dashboard_view(request):
    school = get_object_or_404(School)
    classes = Class.objects.filter(school=school, is_active=True)
    
    context = {
        'classes': classes,
        'current_term': school.current_term,
        'academic_year': school.academic_year,
    }
    
    return render(request, 'reports/dashboard.html', context)

@login_required
def class_report_view(request, class_id):
    school = get_object_or_404(School)
    class_obj = get_object_or_404(Class, id=class_id, school=school)
    
    results = StudentTermResult.objects.filter(
        student__current_class=class_obj,
        term=school.current_term,
        academic_year=school.academic_year
    ).order_by('position')
    
    subjects = Subject.objects.filter(
        school=school,
        is_active=True,
        score__student__current_class=class_obj,
        score__term=school.current_term,
        score__academic_year=school.academic_year
    ).distinct()
    
    # Calculate class statistics
    if results:
        class_average = sum(result.average_score for result in results) / len(results)
        highest_score = max(result.average_score for result in results)
        lowest_score = min(result.average_score for result in results)
    else:
        class_average = highest_score = lowest_score = 0
    
    context = {
        'class_obj': class_obj,
        'results': results,
        'subjects': subjects,
        'class_average': class_average,
        'highest_score': highest_score,
        'lowest_score': lowest_score,
        'total_students': results.count(),
    }
    
    return render(request, 'reports/class_report.html', context)

@login_required
def student_report_view(request, student_id):
    school = get_object_or_404(School)
    student = get_object_or_404(Student, id=student_id, school=school)
    
    try:
        result = StudentTermResult.objects.get(
            student=student,
            term=school.current_term,
            academic_year=school.academic_year
        )
    except StudentTermResult.DoesNotExist:
        result = None
    
    scores = Score.objects.filter(
        student=student,
        term=school.current_term,
        academic_year=school.academic_year
    ).order_by('subject__name')
    
    context = {
        'student': student,
        'result': result,
        'scores': scores,
        'school': school,
    }
    
    return render(request, 'reports/student_report.html', context)

@login_required
def generate_class_reports_view(request, class_id):
    school = get_object_or_404(School)
    class_obj = get_object_or_404(Class, id=class_id, school=school)
    
    students = Student.objects.filter(current_class=class_obj, is_active=True)
    
    if request.method == 'POST':
        # Create a ZIP file containing all student reports
        zip_buffer = BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for student in students:
                try:
                    result = StudentTermResult.objects.get(
                        student=student,
                        term=school.current_term,
                        academic_year=school.academic_year
                    )
                except StudentTermResult.DoesNotExist:
                    continue
                
                scores = Score.objects.filter(
                    student=student,
                    term=school.current_term,
                    academic_year=school.academic_year
                ).order_by('subject__name')
                
                # Generate PDF for this student
                pdf_file = generate_student_report_pdf_professional(student, result, scores, school)

                # Add to ZIP
                filename = f"{student.full_name.replace(' ', '_')}_report.pdf"
                zip_file.writestr(filename, pdf_file)
        
        zip_buffer.seek(0)
        
        response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename="{class_obj.name}_reports.zip"'
        
        messages.success(request, f'Generated reports for {students.count()} students in {class_obj.name}')
        return response
    
    return render(request, 'reports/generate_class_reports.html', {
        'class_obj': class_obj,
        'students': students,
    })

@login_required
def download_student_report_view(request, student_id):
    school = get_object_or_404(School)
    student = get_object_or_404(Student, id=student_id, school=school)
    
    try:
        result = StudentTermResult.objects.get(
            student=student,
            term=school.current_term,
            academic_year=school.academic_year
        )
    except StudentTermResult.DoesNotExist:
        messages.error(request, 'No results found for this student.')
        return redirect('reports_dashboard')
    
    scores = Score.objects.filter(
        student=student,
        term=school.current_term,
        academic_year=school.academic_year
    ).order_by('subject__name')
    
    pdf_file = generate_student_report_pdf_professional(student, result, scores, school)

    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{student.full_name}_report.pdf"'

    return response


def generate_bulk_reports_zip(students, school):
    """Generate a ZIP file containing PDF reports for multiple students"""
    from io import BytesIO
    import zipfile
    from django.http import HttpResponse

    # Create ZIP file in memory
    zip_buffer = BytesIO()

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for student in students:
            try:
                # Get student's scores
                scores = Score.objects.filter(
                    student=student,
                    is_submitted=True,
                    term=school.current_term,
                    academic_year=school.academic_year
                ).select_related('subject').order_by('subject__name')

                if not scores.exists():
                    continue

                # Get or create term result
                term_result, created = StudentTermResult.objects.get_or_create(
                    student=student,
                    term=school.current_term,
                    academic_year=school.academic_year,
                    defaults={
                        'total_score': sum(score.total_score for score in scores),
                        'average_score': sum(score.total_score for score in scores) / scores.count(),
                        'subjects_count': scores.count(),
                    }
                )

                if not created:
                    # Update existing result
                    term_result.total_score = sum(score.total_score for score in scores)
                    term_result.average_score = sum(score.total_score for score in scores) / scores.count()
                    term_result.subjects_count = scores.count()
                    term_result.save()

                # Calculate position
                term_result.calculate_position()

                # Generate PDF for this student
                pdf_content = generate_student_report_pdf_professional(student, term_result, scores, school)

                # Add to ZIP with clean filename
                filename = f"{student.full_name.replace(' ', '_').replace('/', '_')}_report.pdf"
                zip_file.writestr(filename, pdf_content)

            except Exception as e:
                # Log error but continue with other students
                print(f"Error generating report for {student.full_name}: {str(e)}")
                continue

    # Prepare response
    zip_buffer.seek(0)
    response = HttpResponse(zip_buffer.read(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename="{school.name.replace(" ", "_")}_reports_{school.current_term}_{school.academic_year}.zip"'

    return response
