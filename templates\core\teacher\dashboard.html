{% extends 'base.html' %}
{% load static %}

{% block title %}Teacher Dashboard{% endblock %}

{% block content %}
<!-- Enhanced Mobile-First Teacher Dashboard -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Mobile-Optimized Header Section -->
    <div class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-10 lg:static">
        <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
            <div class="flex flex-col space-y-4 py-4 sm:py-6">
                <!-- Welcome Message -->
                <div class="text-center sm:text-left">
                    <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                        Welcome back, {{ user.first_name }}! 👋
                    </h1>
                    <p class="text-xs sm:text-sm lg:text-base text-gray-600 mt-1">
                        {{ school.name|truncatechars:30 }} • {{ school.current_term }} {{ school.academic_year }}
                    </p>
                </div>

                <!-- Quick Action Buttons - Mobile First -->
                <div class="flex flex-wrap gap-2 justify-center sm:justify-start">
                    <a href="{% url 'profile_edit' %}"
                       class="inline-flex items-center justify-center px-3 py-2 bg-gray-100 text-gray-700 text-xs sm:text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors">
                        <i data-lucide="user" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                        Profile
                    </a>
                    <button onclick="window.location.reload()"
                            class="inline-flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 text-xs sm:text-sm font-medium rounded-lg hover:bg-blue-200 transition-colors">
                        <i data-lucide="refresh-cw" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content - Mobile Optimized -->
    <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <!-- Enhanced Mobile-First Stats Grid -->
        <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
            <!-- My Classes -->
            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-2 sm:mb-0">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Classes</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-blue-600">{{ total_classes }}</p>
                    </div>
                    <div class="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 bg-blue-100 rounded-lg flex items-center justify-center self-end sm:self-auto">
                        <i data-lucide="school" class="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-blue-600"></i>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-1">Classes assigned</p>
            </div>

            <!-- My Subjects -->
            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-2 sm:mb-0">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Subjects</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-green-600">{{ total_subjects }}</p>
                    </div>
                    <div class="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 bg-green-100 rounded-lg flex items-center justify-center self-end sm:self-auto">
                        <i data-lucide="book-open" class="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-green-600"></i>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-1">Subjects teaching</p>
            </div>

            <!-- Total Students -->
            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-2 sm:mb-0">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Students</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-purple-600">{{ total_students }}</p>
                    </div>
                    <div class="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 bg-purple-100 rounded-lg flex items-center justify-center self-end sm:self-auto">
                        <i data-lucide="users" class="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-purple-600"></i>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-1">Total students</p>
            </div>

            <!-- Completion Rate -->
            <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6 hover:shadow-md transition-shadow">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-2 sm:mb-0">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Progress</p>
                        <p class="text-lg sm:text-xl lg:text-2xl font-bold text-orange-600">{{ completion_rate }}%</p>
                    </div>
                    <div class="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 bg-orange-100 rounded-lg flex items-center justify-center self-end sm:self-auto">
                        <i data-lucide="trending-up" class="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-orange-600"></i>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-1">Score completion</p>
            </div>
        </div>

        <!-- Mobile-First Quick Actions -->
        <div class="mb-6 sm:mb-8">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 px-1">Quick Actions</h2>
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
                <a href="#grade-entry" onclick="scrollToSection('grade-entry')"
                   class="flex flex-col items-center p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-blue-200 transition-all">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                        <i data-lucide="edit-3" class="w-4 h-4 sm:w-5 sm:h-5 text-blue-600"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-700 text-center">Grade Entry</span>
                </a>

                <a href="#my-classes" onclick="scrollToSection('my-classes')"
                   class="flex flex-col items-center p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-green-200 transition-all">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center mb-2">
                        <i data-lucide="users" class="w-4 h-4 sm:w-5 sm:h-5 text-green-600"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-700 text-center">My Classes</span>
                </a>

                <a href="#reports" onclick="scrollToSection('reports')"
                   class="flex flex-col items-center p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-purple-200 transition-all">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-2">
                        <i data-lucide="file-text" class="w-4 h-4 sm:w-5 sm:h-5 text-purple-600"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-700 text-center">Reports</span>
                </a>

                <a href="{% url 'profile_edit' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-gray-300 transition-all">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                        <i data-lucide="user" class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-700 text-center">Profile</span>
                </a>

                <button onclick="window.location.reload()"
                        class="flex flex-col items-center p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-blue-200 transition-all">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4 sm:w-5 sm:h-5 text-blue-600"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-700 text-center">Refresh</span>
                </button>

                <a href="{% url 'logout' %}"
                   class="flex flex-col items-center p-3 sm:p-4 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md hover:border-red-200 transition-all">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-red-100 rounded-lg flex items-center justify-center mb-2">
                        <i data-lucide="log-out" class="w-4 h-4 sm:w-5 sm:h-5 text-red-600"></i>
                    </div>
                    <span class="text-xs sm:text-sm font-medium text-gray-700 text-center">Logout</span>
                </a>
            </div>
        </div>

        <!-- Enhanced Mobile-First My Classes Section -->
        <div id="my-classes" class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-3 sm:px-4 lg:px-6 py-3 sm:py-4 border-b border-gray-100">
                <div class="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h2 class="text-lg sm:text-xl font-semibold text-gray-900 flex items-center">
                            <i data-lucide="school" class="w-5 h-5 mr-2 text-blue-600"></i>
                            My Classes
                        </h2>
                        <p class="text-xs sm:text-sm text-gray-600 mt-1">Tap a class to manage scores and view reports</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs sm:text-sm text-gray-500">{{ classes_data|length }} class{{ classes_data|length|pluralize:"es" }}</span>
                    </div>
                </div>
            </div>

            {% if classes_data %}
            <div class="p-3 sm:p-4 lg:p-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
                    {% for class_name, class_data in classes_data.items %}
                    <div class="bg-gradient-to-br from-white to-gray-50 rounded-lg sm:rounded-xl border border-gray-200 p-4 sm:p-5 lg:p-6 hover:shadow-md transition-all duration-200 hover:border-blue-200 active:scale-95">
                        <!-- Mobile-Optimized Class Header -->
                        <div class="flex items-start justify-between mb-3 sm:mb-4">
                            <div class="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg sm:rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-white text-sm sm:text-lg font-bold">{{ class_name|slice:":2" }}</span>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <h3 class="text-base sm:text-lg font-semibold text-gray-900 truncate">{{ class_name }}</h3>
                                    {% if class_data.is_class_teacher %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                        <i data-lucide="star" class="w-3 h-3 mr-1"></i>
                                        Class Teacher
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Mobile-Friendly Class Stats -->
                        <div class="grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                            <div class="text-center bg-white rounded-lg p-2 sm:p-3 border border-gray-100">
                                <p class="text-lg sm:text-2xl font-bold text-gray-900">{{ class_data.student_count }}</p>
                                <p class="text-xs text-gray-600">Students</p>
                            </div>
                            <div class="text-center bg-white rounded-lg p-2 sm:p-3 border border-gray-100">
                                <p class="text-lg sm:text-2xl font-bold text-gray-900">{{ class_data.subjects|length }}</p>
                                <p class="text-xs text-gray-600">Subjects</p>
                            </div>
                        </div>

                        <!-- Mobile-Optimized Teaching Subjects -->
                        <div class="mb-3 sm:mb-4">
                            <p class="text-xs font-medium text-gray-600 uppercase tracking-wider mb-2">Teaching Subjects:</p>
                            <div class="flex flex-wrap gap-1">
                                {% for subject in class_data.subjects %}
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    {{ subject.name|truncatechars:12 }}
                                </span>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Mobile-First Action Buttons -->
                        <div class="space-y-2">
                            <!-- Primary Actions -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                <a href="{% url 'teacher_class_subjects' class_data.class_obj.id %}"
                                   class="inline-flex items-center justify-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors active:scale-95">
                                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                    Enter Scores
                                </a>
                                <a href="{% url 'teacher_class_reports' class_data.class_obj.id %}"
                                   class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors active:scale-95">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-2"></i>
                                    Reports
                                </a>
                            </div>

                            {% if class_data.is_class_teacher %}
                            <!-- Class Teacher Specific Actions -->
                            <div class="pt-2 border-t border-gray-200">
                                <div class="grid grid-cols-1 gap-2">
                                    <a href="{% url 'student_management' class_data.class_obj.id %}"
                                       class="inline-flex items-center justify-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors active:scale-95">
                                        <i data-lucide="clipboard-list" class="w-4 h-4 mr-2"></i>
                                        Attendance & Comments
                                </a>
                                <a href="{% url 'teacher_all_subjects_access' class_data.class_obj.id %}"
                                   class="inline-flex items-center justify-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors">
                                    <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                                    All Subjects
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <!-- Empty State -->
            <div class="p-12 text-center">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="school" class="w-10 h-10 text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Classes Assigned</h3>
                <p class="text-gray-600 mb-4 max-w-sm mx-auto">
                    You haven't been assigned to any classes yet. Contact your school administrator to get class assignments.
                </p>
                <a href="mailto:admin@{{ school.name|lower }}.com"
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    <i data-lucide="mail" class="w-4 h-4 mr-2"></i>
                    Contact Admin
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-200 hover:border-blue-200">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="user" class="w-6 h-6 text-gray-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">My Profile</h3>
                        <p class="text-sm text-gray-600">Update your information</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-200 hover:border-green-200">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="help-circle" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Help & Support</h3>
                        <p class="text-sm text-gray-600">Get help using the system</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-all duration-200 hover:border-purple-200">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                        <i data-lucide="bell" class="w-6 h-6 text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
                        <p class="text-sm text-gray-600">View system updates</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Enhanced Mobile-First Dashboard JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Lucide icons
        lucide.createIcons();

        // Mobile-friendly smooth scrolling
        window.scrollToSection = function(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                const headerOffset = 80; // Account for sticky header
                const elementPosition = element.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        };

        // Add touch feedback for mobile interactions
        const touchElements = document.querySelectorAll('.active\\:scale-95');
        touchElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Mobile-optimized loading states
        const actionButtons = document.querySelectorAll('a[href*="teacher"]');
        actionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const originalText = this.innerHTML;
                this.innerHTML = '<i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>Loading...';
                this.style.pointerEvents = 'none';

                // Restore after a short delay if navigation fails
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.pointerEvents = '';
                    lucide.createIcons();
                }, 3000);
            });
        });

        // Add swipe gestures for mobile navigation (optional enhancement)
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 100;
            const swipeDistance = touchEndX - touchStartX;

            if (Math.abs(swipeDistance) > swipeThreshold) {
                if (swipeDistance > 0) {
                    // Swipe right - could trigger menu or back action
                    console.log('Swipe right detected');
                } else {
                    // Swipe left - could trigger forward action
                    console.log('Swipe left detected');
                }
            }
        }

        // Auto-refresh data every 5 minutes for mobile users
        if (window.innerWidth <= 768) {
            setInterval(function() {
                // Subtle indication of data refresh
                const refreshBtn = document.querySelector('[onclick*="reload"]');
                if (refreshBtn) {
                    refreshBtn.classList.add('animate-pulse');
                    setTimeout(() => {
                        refreshBtn.classList.remove('animate-pulse');
                    }, 1000);
                }
            }, 300000); // 5 minutes
        }
    });
</script>
{% endblock %}