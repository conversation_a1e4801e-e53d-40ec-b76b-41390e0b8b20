{% extends 'base.html' %}
{% load static %}

{% block title %}Manage Teachers - {{ school.name }}{% endblock %}

{% block content %}
{% csrf_token %}
<div class="min-h-screen bg-gray-50 py-4 sm:py-8">
    <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        <!-- Mobile-First Header -->
        <div class="mb-6 sm:mb-8">
            <div class="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
                <div class="text-center sm:text-left">
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Manage Teachers</h1>
                    <p class="mt-2 text-sm sm:text-base text-gray-600">Add, assign, and manage teachers for {{ school.name|truncatechars:30 }}</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <a href="{% url 'add_teacher' %}"
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center active:scale-95">
                        <i data-lucide="user-plus" class="w-4 h-4 inline mr-2"></i>
                        Add Teacher
                    </a>
                    <a href="{% url 'assign_teacher' %}"
                       class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center active:scale-95">
                        <i data-lucide="users" class="w-4 h-4 inline mr-2"></i>
                        Assign Teacher
                    </a>
                </div>
            </div>
        </div>

        <!-- Mobile-First Teachers List -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200">
            <div class="p-4 sm:p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i data-lucide="users" class="w-5 h-5 mr-2 text-blue-600"></i>
                        Current Teachers
                    </h2>
                    <span class="text-sm text-gray-500">{{ teachers_data|length }} teacher{{ teachers_data|length|pluralize }}</span>
                </div>
            </div>

            {% if teachers_data %}
            <!-- Mobile Card Layout (default) -->
            <div class="block lg:hidden">
                <div class="divide-y divide-gray-200">
                    {% for teacher_data in teachers_data %}
                    <div class="p-4 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start space-x-3">
                            <!-- Teacher Avatar -->
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold text-sm">
                                    {{ teacher_data.teacher.first_name|slice:":1" }}{{ teacher_data.teacher.last_name|slice:":1" }}
                                </span>
                            </div>

                            <!-- Teacher Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-base font-semibold text-gray-900 truncate">
                                            {{ teacher_data.teacher.get_full_name }}
                                        </h3>
                                        <p class="text-sm text-gray-600 truncate">{{ teacher_data.teacher.email }}</p>
                                        {% if teacher_data.teacher.phone_number %}
                                        <p class="text-xs text-gray-500">{{ teacher_data.teacher.phone_number }}</p>
                                        {% endif %}
                                    </div>

                                    <!-- Actions Dropdown -->
                                    <div class="relative ml-2">
                                        <button onclick="toggleDropdown('teacher-{{ forloop.counter }}')"
                                                class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                                            <i data-lucide="more-vertical" class="w-4 h-4"></i>
                                        </button>
                                        <div id="teacher-{{ forloop.counter }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                                            <div class="py-1">
                                                <a href="{% url 'teacher_detail' teacher_data.teacher.id %}"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i data-lucide="eye" class="w-4 h-4 mr-2"></i>
                                                    View Details
                                                </a>
                                                <a href="{% url 'teacher_edit' teacher_data.teacher.id %}"
                                                   class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                                    Edit
                                                </a>
                                                <button onclick="resetTeacherPassword({{ teacher_data.teacher.id }})"
                                                        class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                                                    <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                                                    Reset Password
                                                </button>
                                                <a href="{% url 'teacher_remove' teacher_data.teacher.id %}"
                                                   class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                                    Remove
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Assignments -->
                                <div class="mt-3">
                                    <div class="flex flex-wrap gap-1">
                                        {% for assignment in teacher_data.assignments %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ assignment.class_name }} - {{ assignment.subject_name }}
                                        </span>
                                        {% empty %}
                                        <span class="text-xs text-gray-500">No assignments</span>
                                        {% endfor %}
                                    </div>

                                    {% if teacher_data.class_teacher_for %}
                                    <div class="mt-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i data-lucide="star" class="w-3 h-3 mr-1"></i>
                                            Class Teacher: {{ teacher_data.class_teacher_for }}
                                        </span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Desktop Table Layout (hidden on mobile) -->
            <div class="hidden lg:block">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="text-left py-3 px-6 font-medium text-gray-700">Teacher</th>
                                <th class="text-left py-3 px-6 font-medium text-gray-700">Contact</th>
                                <th class="text-left py-3 px-6 font-medium text-gray-700">Assignments</th>
                                <th class="text-left py-3 px-6 font-medium text-gray-700">Class Teacher</th>
                                <th class="text-center py-3 px-6 font-medium text-gray-700">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            {% for teacher_data in teachers_data %}
                        <tr class="hover:bg-gray-50">
                            <td class="py-4 px-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-blue-600 font-medium">
                                            {{ teacher_data.teacher.first_name|first }}{{ teacher_data.teacher.last_name|first }}
                                        </span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ teacher_data.teacher.get_full_name }}
                                        </p>
                                        <p class="text-sm text-gray-500">{{ teacher_data.teacher.username }}</p>
                                    </div>
                                </div>
                            </td>
                            <td class="py-4 px-6">
                                <p class="text-sm text-gray-900">{{ teacher_data.teacher.email }}</p>
                                {% if teacher_data.profile.phone_number %}
                                <p class="text-sm text-gray-500">{{ teacher_data.profile.phone_number }}</p>
                                {% endif %}
                            </td>
                            <td class="py-4 px-6">
                                <div class="space-y-1">
                                    {% for assignment in teacher_data.assignments %}
                                    <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                                        {{ assignment.class_assigned.name }} - {{ assignment.subject.name }}
                                    </span>
                                    {% endfor %}
                                </div>
                            </td>
                            <td class="py-4 px-6">
                                {% if teacher_data.is_class_teacher %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i data-lucide="check" class="w-3 h-3 mr-1"></i>
                                    Yes
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    No
                                </span>
                                {% endif %}
                            </td>
                            <td class="py-4 px-6 text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <a href="{% url 'teacher_detail' teacher_data.teacher.id %}"
                                       class="text-blue-600 hover:text-blue-700 text-sm"
                                       title="View Details">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                    </a>
                                    <button onclick="editTeacher({{ teacher_data.teacher.id }})"
                                            class="text-green-600 hover:text-green-700 text-sm"
                                            title="Edit Teacher">
                                        <i data-lucide="edit" class="w-4 h-4"></i>
                                    </button>
                                    <button onclick="resetTeacherPassword({{ teacher_data.teacher.id }})"
                                            class="text-orange-600 hover:text-orange-700 text-sm"
                                            title="Reset Password">
                                        <i data-lucide="key" class="w-4 h-4"></i>
                                    </button>
                                    <button onclick="removeTeacher({{ teacher_data.teacher.id }})"
                                            class="text-red-600 hover:text-red-700 text-sm"
                                            title="Remove Teacher">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-12">
                <i data-lucide="users" class="w-12 h-12 mx-auto text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Teachers Yet</h3>
                <p class="text-gray-500 mb-4">Start by adding teachers to your school.</p>
                <a href="{% url 'add_teacher' %}"
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i data-lucide="user-plus" class="w-4 h-4 inline mr-2"></i>
                    Add First Teacher
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    // Initialize Lucide icons
    lucide.createIcons();
    
    function editTeacher(teacherId) {
        window.location.href = `/school-admin/teachers/${teacherId}/edit/`;
    }

    function removeTeacher(teacherId) {
        if (confirm('Are you sure you want to remove this teacher? This will remove all their assignments.')) {
            window.location.href = `/school-admin/teachers/${teacherId}/remove/`;
        }
    }

    function resetTeacherPassword(teacherId) {
        if (confirm('Are you sure you want to reset this teacher\'s password? They will need to use the new password to log in.')) {
            fetch(`/school-admin/teachers/${teacherId}/reset-password/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Password reset successfully! New password: teacher123\n\nPlease inform the teacher of their new password.');
                } else {
                    alert('Error resetting password: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error resetting password. Please try again.');
            });
        }
    }

    // Mobile dropdown functionality
    function toggleDropdown(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        const allDropdowns = document.querySelectorAll('[id^="teacher-"]');

        // Close all other dropdowns
        allDropdowns.forEach(d => {
            if (d.id !== dropdownId) {
                d.classList.add('hidden');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('hidden');
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('[id^="teacher-"]');
        const isDropdownButton = event.target.closest('[onclick*="toggleDropdown"]');

        if (!isDropdownButton) {
            dropdowns.forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });

    // Initialize Lucide icons
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
    });
</script>
{% endblock %}
