from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from .models import *

# Removed duplicate SchoolSetupForm - using the comprehensive one below



class ClassForm(forms.ModelForm):
    class Meta:
        model = Class
        fields = ['name']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
                'placeholder': 'Class name (e.g., Basic 1, JHS 2)'
            }),
        }

class SubjectForm(forms.ModelForm):
    class Meta:
        model = Subject
        fields = ['name', 'code']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
                'placeholder': 'Subject name (e.g., Mathematics)'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
                'placeholder': 'Code (e.g., MATH)'
            }),
        }

class StudentForm(forms.ModelForm):
    class Meta:
        model = Student
        fields = ['first_name', 'last_name', 'gender', 'parent_name', 'parent_phone', 'current_class', 'profile_picture', 'id_card_image']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'last_name': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'gender': forms.Select(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'profile_picture': forms.FileInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500', 'accept': 'image/*'}),
            'id_card_image': forms.FileInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500', 'accept': 'image/*'}),
            'parent_name': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'parent_phone': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'current_class': forms.Select(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
        }

# ScoreEntryForm removed - replaced with SpreadsheetScoreForm for flexible grading

class TermConfigurationForm(forms.ModelForm):
    class Meta:
        model = TermConfiguration
        fields = ['term', 'academic_year', 'start_date', 'end_date', 'next_term_reopening_date', 'is_active']
        widgets = {
            'term': forms.Select(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'academic_year': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500', 'placeholder': '2023/2024'}),
            'start_date': forms.DateInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500', 'type': 'date'}),
            'next_term_reopening_date': forms.DateInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'rounded border-gray-300 text-blue-600 focus:ring-blue-500'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        # Ensure both dates are provided
        if not start_date:
            raise forms.ValidationError('Start date is required.')

        if not end_date:
            raise forms.ValidationError('End date is required.')

        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError('End date must be after start date.')

        return cleaned_data

class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm',
            'placeholder': 'Email address'
        })
    )
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm',
            'placeholder': 'First name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm',
            'placeholder': 'Last name'
        })
    )

    class Meta:
        model = User
        fields = ("username", "email", "first_name", "last_name", "password1", "password2")
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm',
                'placeholder': 'Username'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['password1'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm',
            'placeholder': 'Password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm',
            'placeholder': 'Confirm password'
        })

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data["email"]
        user.first_name = self.cleaned_data["first_name"]
        user.last_name = self.cleaned_data["last_name"]
        if commit:
            user.save()
        return user

class UserProfileForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ['user_type', 'phone_number', 'employee_id']
        widgets = {
            'user_type': forms.Select(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'phone_number': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
            'employee_id': forms.TextInput(attrs={'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'}),
        }

class TeacherAssignmentForm(forms.Form):
    """Enhanced form for assigning teachers to multiple subjects"""
    teacher = forms.ModelChoiceField(
        queryset=None,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 pr-12 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 appearance-none'
        }),
        empty_label="Choose a teacher"
    )

    class_assigned = forms.ModelChoiceField(
        queryset=None,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 pr-12 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 appearance-none'
        }),
        empty_label="Choose a class"
    )

    subjects = forms.ModelMultipleChoiceField(
        queryset=None,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'rounded border-gray-300 text-purple-600 focus:ring-purple-500'
        }),
        required=False,
        help_text="Select one or more subjects. If 'Class Teacher' is checked, all subjects will be automatically assigned."
    )

    is_class_teacher = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'rounded border-gray-300 text-purple-600 focus:ring-purple-500',
            'id': 'is_class_teacher_checkbox'
        }),
        help_text="Class teachers are automatically assigned to all subjects and can manage attendance and comments."
    )

    def __init__(self, school=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if school:
            # Set querysets for school-specific data
            self.fields['teacher'].queryset = User.objects.filter(
                profile__user_type='teacher',
                profile__is_active=True,
                is_active=True
            ).select_related('profile')
            self.fields['class_assigned'].queryset = Class.objects.filter(
                school=school, is_active=True
            ).order_by('name')
            self.fields['subjects'].queryset = Subject.objects.filter(
                school=school, is_active=True
            ).order_by('name')

    def clean(self):
        cleaned_data = super().clean()
        subjects = cleaned_data.get('subjects')
        is_class_teacher = cleaned_data.get('is_class_teacher')

        # If not class teacher, at least one subject must be selected
        if not is_class_teacher and not subjects:
            raise forms.ValidationError("Please select at least one subject or check 'Class Teacher' to assign all subjects.")

        return cleaned_data

    def __init__(self, *args, **kwargs):
        school = kwargs.pop('school', None)
        super().__init__(*args, **kwargs)

        if school:
            # Get teachers who have any assignment in this school (including basic assignments)
            # This ensures we show teachers who were added to this school
            school_teacher_ids = TeacherAssignment.objects.filter(
                school=school
            ).values_list('teacher_id', flat=True).distinct()

            self.fields['teacher'].queryset = User.objects.filter(
                id__in=school_teacher_ids,
                profile__user_type='teacher',
                profile__is_active=True,
                is_active=True
            ).order_by('first_name', 'last_name')

            # If no teachers found, show all teachers (for initial setup)
            if not self.fields['teacher'].queryset.exists():
                self.fields['teacher'].queryset = User.objects.filter(
                    profile__user_type='teacher',
                    profile__is_active=True,
                    is_active=True
                ).order_by('first_name', 'last_name')

            # Filter classes and subjects to this school
            self.fields['class_assigned'].queryset = Class.objects.filter(school=school, is_active=True)
            self.fields['subjects'].queryset = Subject.objects.filter(school=school, is_active=True)


class SchoolAdminRegistrationForm(UserCreationForm):
    """Form for school administrator registration"""
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
            'placeholder': 'First name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
            'placeholder': 'Last name'
        })
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
            'placeholder': 'Email address'
        })
    )
    phone_number = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
            'placeholder': 'Phone number'
        })
    )

    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
                'placeholder': 'Username'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['password1'].widget.attrs.update({
            'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
            'placeholder': 'Password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'w-full pl-10 pr-3 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm',
            'placeholder': 'Confirm password'
        })

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")
        return email

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']

        if commit:
            user.save()
            # Create user profile
            UserProfile.objects.create(
                user=user,
                user_type='school_admin',
                phone_number=self.cleaned_data['phone_number']
            )
        return user


class SubjectForm(forms.ModelForm):
    """Form for adding/editing subjects"""
    class Meta:
        model = Subject
        fields = ['name', 'code']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Subject name (e.g., Mathematics)'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Subject code (e.g., MATH)'
            }),
        }


class ClassForm(forms.ModelForm):
    """Form for adding/editing classes"""
    class Meta:
        model = Class
        fields = ['name']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Class name (e.g., Basic 1A)'
            }),
        }


class SchoolSettingsForm(forms.Form):
    """Comprehensive form for school settings including email configuration"""

    # School Information Fields
    name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
            'placeholder': 'School Name'
        })
    )

    motto = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
            'placeholder': 'School Motto (optional)'
        })
    )

    phone_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
            'placeholder': 'School Phone Number'
        })
    )

    address = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 resize-none',
            'placeholder': 'School Address',
            'rows': 3
        })
    )

    location = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
            'placeholder': 'City, Region'
        })
    )

    academic_year = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
            'placeholder': '2024/2025'
        })
    )

    current_term = forms.ChoiceField(
        choices=[
            ('First Term', 'First Term'),
            ('Second Term', 'Second Term'),
            ('Third Term', 'Third Term'),
        ],
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400'
        })
    )

    next_term_reopening_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
            'type': 'date'
        }),
        help_text='Date when school reopens for the next term (shown on report cards)'
    )

    logo = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 file:mr-3 file:py-2 file:px-4 file:rounded-xl file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100',
            'accept': 'image/*'
        })
    )

    # Email notification preferences (SMTP is configured by site admin)

    # Notification Settings
    send_welcome_emails = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-5 h-5 text-purple-600 border-2 border-slate-300 rounded focus:ring-purple-500 focus:ring-2'
        })
    )

    send_password_reset_emails = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'w-5 h-5 text-purple-600 border-2 border-slate-300 rounded focus:ring-purple-500 focus:ring-2'
        })
    )

    # Grading System
    grading_system = forms.ChoiceField(
        choices=[
            ('waec', 'WAEC (A1-F9) - For SHS'),
            ('ges_bece', 'GES BECE (1-9) - For Basic Schools'),
            ('alphabetic', 'Alphabetic (A-F)'),
            ('numeric', 'Numeric (1-5)'),
            ('percentage', 'Percentage'),
        ],
        initial='waec',
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400'
        })
    )

    # Report Card Template
    report_template = forms.ModelChoiceField(
        queryset=None,  # Will be set in __init__
        required=False,
        empty_label="Select a report card template",
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400'
        }),
        help_text='Choose your preferred report card template design'
    )

    def __init__(self, school=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set up report template queryset
        from .models import SystemReportCardTemplate
        self.fields['report_template'].queryset = SystemReportCardTemplate.objects.filter(is_active=True)

        if school:
            # Pre-populate school fields
            self.fields['name'].initial = school.name
            self.fields['motto'].initial = school.motto
            self.fields['phone_number'].initial = school.phone_number
            self.fields['address'].initial = school.address
            self.fields['location'].initial = school.location
            self.fields['academic_year'].initial = school.academic_year
            self.fields['current_term'].initial = school.current_term
            self.fields['next_term_reopening_date'].initial = school.next_term_reopening_date

            # Get or create school settings
            from .models import SchoolSettings
            settings = SchoolSettings.get_for_school(school)

            # Pre-populate settings fields
            self.fields['email_host'].initial = settings.email_host
            self.fields['email_port'].initial = settings.email_port
            self.fields['email_host_user'].initial = settings.email_host_user
            self.fields['email_use_tls'].initial = settings.email_use_tls
            self.fields['send_welcome_emails'].initial = settings.send_welcome_emails
            self.fields['send_password_reset_emails'].initial = settings.send_password_reset_emails
            self.fields['grading_system'].initial = settings.grading_system

            # Set current report template
            if hasattr(school, 'report_template') and school.report_template:
                self.fields['report_template'].initial = school.report_template

    def save(self, school):
        """Save form data to school and settings models"""
        if not school:
            return None

        # Update school fields
        school.name = self.cleaned_data['name']
        school.motto = self.cleaned_data['motto']
        school.phone_number = self.cleaned_data['phone_number']
        school.address = self.cleaned_data['address']
        school.location = self.cleaned_data['location']
        school.academic_year = self.cleaned_data['academic_year']
        school.current_term = self.cleaned_data['current_term']
        school.next_term_reopening_date = self.cleaned_data['next_term_reopening_date']

        # Handle logo upload
        if 'logo' in self.cleaned_data and self.cleaned_data['logo']:
            school.logo = self.cleaned_data['logo']

        # Handle report template selection
        if 'report_template' in self.cleaned_data and self.cleaned_data['report_template']:
            school.report_template = self.cleaned_data['report_template']

        school.save()

        # Update school settings (notification preferences only, not SMTP)
        from .models import SchoolSettings
        settings = SchoolSettings.get_for_school(school)

        # Only update notification preferences, not SMTP settings
        settings.send_welcome_emails = self.cleaned_data['send_welcome_emails']
        settings.send_password_reset_emails = self.cleaned_data['send_password_reset_emails']
        settings.grading_system = self.cleaned_data['grading_system']

        settings.save()

        return school, settings




# Keep the original SchoolSetupForm for backward compatibility
class SchoolSetupForm(forms.ModelForm):
    """Simple form for school profile setup (backward compatibility)"""

    class Meta:
        model = School
        fields = [
            'name', 'motto', 'logo', 'phone_number',
            'address', 'location', 'academic_year', 'current_term'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
                'placeholder': 'School Name'
            }),
            'motto': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
                'placeholder': 'School Motto (optional)'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
                'placeholder': 'School Phone Number'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 resize-none',
                'placeholder': 'School Address',
                'rows': 3
            }),
            'location': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
                'placeholder': 'City, Region'
            }),
            'academic_year': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
                'placeholder': '2024/2025'
            }),
            'current_term': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400'
            }),
            'logo': forms.FileInput(attrs={
                'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 file:mr-3 file:py-2 file:px-4 file:rounded-xl file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100',
                'accept': 'image/*'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Load current email settings from Django settings
        from django.conf import settings

        # Pre-populate email fields with current settings
        self.fields['email_host'].initial = getattr(settings, 'EMAIL_HOST', 'smtp.gmail.com')
        self.fields['email_port'].initial = getattr(settings, 'EMAIL_PORT', 587)
        self.fields['email_host_user'].initial = getattr(settings, 'EMAIL_HOST_USER', '')
        self.fields['email_use_tls'].initial = getattr(settings, 'EMAIL_USE_TLS', True)

        # Don't pre-populate password for security

    # No custom save method needed - SMTP is handled by site admin


class GradingFieldForm(forms.ModelForm):
    """Form for configuring grading fields"""
    class Meta:
        model = SchoolGradingField
        fields = ['name', 'code', 'percentage', 'max_score', 'order']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Field Name (e.g., Classwork)'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Code (e.g., CW)'
            }),
            'percentage': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 0,
                'max': 100,
                'placeholder': 'Percentage'
            }),
            'max_score': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 1,
                'placeholder': 'Maximum Score'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': 1,
                'placeholder': 'Display Order'
            }),
        }


class TeacherClassSubjectSelectionForm(forms.Form):
    """Form for teachers to select class and subject for score entry"""

    def __init__(self, teacher=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if teacher:
            # Get teacher's assignments
            from .models import TeacherAssignment
            assignments = TeacherAssignment.objects.filter(
                teacher=teacher,
                is_active=True
            ).select_related('class_assigned', 'subject', 'school')

            # Create choices for class-subject combinations
            choices = []
            for assignment in assignments:
                choice_value = f"{assignment.class_assigned.id}_{assignment.subject.id}"
                choice_label = f"{assignment.class_assigned.name} - {assignment.subject.name}"
                if assignment.is_class_teacher:
                    choice_label += " (Class Teacher)"
                choices.append((choice_value, choice_label))

            self.fields['class_subject'] = forms.ChoiceField(
                choices=[('', 'Select Class and Subject')] + choices,
                widget=forms.Select(attrs={
                    'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-blue-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400',
                    'onchange': 'this.form.submit();'
                }),
                help_text="Select the class and subject you want to enter scores for"
            )


class SpreadsheetScoreForm(forms.Form):
    """Enhanced dynamic form for spreadsheet-style score entry"""

    def __init__(self, *args, **kwargs):
        students = kwargs.pop('students', [])
        grading_fields = kwargs.pop('grading_fields', [])
        existing_scores = kwargs.pop('existing_scores', {})
        class_obj = kwargs.pop('class_obj', None)
        subject = kwargs.pop('subject', None)
        super().__init__(*args, **kwargs)

        # Add class and subject info as hidden fields
        if class_obj:
            self.fields['class_id'] = forms.CharField(
                initial=class_obj.id,
                widget=forms.HiddenInput()
            )
        if subject:
            self.fields['subject_id'] = forms.CharField(
                initial=subject.id,
                widget=forms.HiddenInput()
            )

        # Create fields for each student and grading field combination
        for student in students:
            for field in grading_fields:
                field_name = f"score_{student.id}_{field.code}"
                current_score = existing_scores.get(student.id, {}).get(field.code, 0)

                self.fields[field_name] = forms.DecimalField(
                    max_digits=5,
                    decimal_places=2,
                    min_value=0,
                    max_value=field.max_score,
                    initial=current_score,
                    required=False,
                    widget=forms.NumberInput(attrs={
                        'class': 'w-full px-3 py-2 border-2 border-slate-300 rounded-xl text-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-slate-400',
                        'step': '0.01',
                        'min': '0',
                        'max': str(field.max_score),
                        'data-student-id': student.id,
                        'data-field-code': field.code,
                        'data-field-percentage': field.percentage,
                        'data-max-score': field.max_score,
                        'placeholder': f'/{field.max_score}',
                        'onchange': 'calculateTotal(this)',
                    })
                )

            # Add a total display field (read-only)
            self.fields[f"total_{student.id}"] = forms.CharField(
                required=False,
                widget=forms.TextInput(attrs={
                    'class': 'w-full px-3 py-2 bg-gray-100 border-2 border-gray-300 rounded-xl text-center font-bold text-blue-600',
                    'readonly': True,
                    'placeholder': '0.0%'
                })
            )

    def get_scores_data(self, students, grading_fields):
        """Extract scores data from form"""
        scores_data = {}

        for student in students:
            student_scores = {}
            has_any_score = False

            for field in grading_fields:
                field_name = f"score_{student.id}_{field.code}"
                score = self.cleaned_data.get(field_name, 0) or 0
                student_scores[field.code] = float(score)

                # Check if student has any non-zero scores
                if float(score) > 0:
                    has_any_score = True

            # Include all students (even with zero scores for consistency)
            scores_data[student.id] = student_scores

        return scores_data


class BulkStudentUploadForm(forms.Form):
    """Enhanced form for bulk student upload via CSV/Excel with template download"""
    file = forms.FileField(
        widget=forms.FileInput(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400 file:mr-3 file:py-2 file:px-4 file:rounded-xl file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100',
            'accept': '.csv,.xlsx,.xls'
        }),
        help_text="Upload CSV or Excel file with student data. Download template below for correct format."
    )

    class_assigned = forms.ModelChoiceField(
        queryset=None,  # Will be set in __init__
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border-2 border-slate-300 rounded-2xl text-slate-900 focus:border-purple-500 focus:ring-0 focus:outline-none transition-all duration-300 bg-white hover:border-slate-400'
        }),
        help_text="Select the class for all students in this upload"
    )

    def __init__(self, school=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if school:
            from .models import Class
            self.fields['class_assigned'].queryset = Class.objects.filter(
                school=school, is_active=True
            ).order_by('name')

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # Check file extension
            allowed_extensions = ['.csv', '.xlsx', '.xls']
            file_extension = file.name.lower().split('.')[-1]
            if f'.{file_extension}' not in allowed_extensions:
                raise ValidationError("Only CSV and Excel files are allowed.")

            # Check file size (max 5MB)
            if file.size > 5 * 1024 * 1024:
                raise ValidationError("File size must be less than 5MB.")

        return file

    def process_upload(self, school):
        """Process the uploaded file and return student data"""
        import pandas as pd
        import io

        file = self.cleaned_data['file']
        class_assigned = self.cleaned_data['class_assigned']

        try:
            # Read file based on extension
            if file.name.endswith('.csv'):
                df = pd.read_csv(io.StringIO(file.read().decode('utf-8')))
            else:
                df = pd.read_excel(file)

            # Validate required columns
            required_columns = ['first_name', 'last_name', 'gender', 'parent_name', 'parent_phone']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                raise ValidationError(f"Missing required columns: {', '.join(missing_columns)}")

            # Process each row
            students_data = []
            errors = []

            for index, row in df.iterrows():
                try:
                    # Validate gender
                    gender = str(row['gender']).upper().strip()
                    if gender not in ['M', 'F', 'MALE', 'FEMALE']:
                        errors.append(f"Row {index + 2}: Invalid gender '{row['gender']}'. Use M/F or Male/Female")
                        continue

                    # Normalize gender
                    gender = 'M' if gender in ['M', 'MALE'] else 'F'

                    # Validate required fields
                    if pd.isna(row['first_name']) or pd.isna(row['last_name']):
                        errors.append(f"Row {index + 2}: First name and last name are required")
                        continue

                    student_data = {
                        'first_name': str(row['first_name']).strip(),
                        'last_name': str(row['last_name']).strip(),
                        'gender': gender,
                        'parent_name': str(row['parent_name']).strip() if not pd.isna(row['parent_name']) else '',
                        'parent_phone': str(row['parent_phone']).strip() if not pd.isna(row['parent_phone']) else '',
                        'current_class': class_assigned,
                        'school': school
                    }

                    students_data.append(student_data)

                except Exception as e:
                    errors.append(f"Row {index + 2}: {str(e)}")

            return students_data, errors

        except Exception as e:
            raise ValidationError(f"Error processing file: {str(e)}")


class TeacherInvitationForm(forms.ModelForm):
    """Form for inviting teachers to a school"""
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'First Name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Last Name'
        })
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
            'placeholder': 'Email Address'
        })
    )
    # Username will be auto-generated based on school and teacher name

    class Meta:
        model = UserProfile
        fields = ['phone_number', 'employee_id']
        widgets = {
            'phone_number': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Phone Number'
            }),
            'employee_id': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Employee ID'
            }),
        }

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")
        return email

    # Username validation removed - auto-generated in view


# System Template Forms (for System Admin)
class SystemClassTemplateForm(forms.ModelForm):
    """Form for managing system-wide class templates"""
    class Meta:
        model = SystemClassTemplate
        fields = ['name', 'description', 'category']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Class name (e.g., Basic 1, JHS 1)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Optional description',
                'rows': 3
            }),
            'category': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            name = name.strip().title()
            # Check for duplicate names (excluding current instance if editing)
            existing = SystemClassTemplate.objects.filter(name__iexact=name)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('A class template with this name already exists.')
        return name


class SystemSubjectTemplateForm(forms.ModelForm):
    """Form for managing system-wide subject templates"""
    class Meta:
        model = SystemSubjectTemplate
        fields = ['name', 'code', 'description', 'category']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Subject name (e.g., Mathematics)'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Subject code (e.g., MATH)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Optional description',
                'rows': 3
            }),
            'category': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            name = name.strip().title()
            # Check for duplicate names (excluding current instance if editing)
            existing = SystemSubjectTemplate.objects.filter(name__iexact=name)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('A subject template with this name already exists.')
        return name

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            code = code.strip().upper()
            # Check for duplicate codes (excluding current instance if editing)
            existing = SystemSubjectTemplate.objects.filter(code__iexact=code)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('A subject template with this code already exists.')
        return code


class SystemGradingTemplateForm(forms.ModelForm):
    """Form for managing system-wide grading templates"""

    # Additional fields for grade boundaries
    grade_boundaries_json = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        help_text="JSON representation of grade boundaries"
    )

    class Meta:
        model = SystemGradingTemplate
        fields = ['name', 'description', 'grading_type', 'grade_boundaries_json']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Template name (e.g., WAEC Standard)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Optional description',
                'rows': 3
            }),
            'grading_type': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Pre-populate grade boundaries for editing
            import json
            self.fields['grade_boundaries_json'].initial = json.dumps(self.instance.grade_boundaries)

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Process grade boundaries
        if self.cleaned_data.get('grade_boundaries_json'):
            import json
            try:
                instance.grade_boundaries = json.loads(self.cleaned_data['grade_boundaries_json'])
            except json.JSONDecodeError:
                # Use default boundaries based on grading type
                instance.grade_boundaries = self.get_default_boundaries(instance.grading_type)
        else:
            # Use default boundaries based on grading type
            instance.grade_boundaries = self.get_default_boundaries(instance.grading_type)

        if commit:
            instance.save()
        return instance

    def get_default_boundaries(self, grading_type):
        """Get default grade boundaries based on grading type"""
        defaults = {
            'waec': {
                'A1': {'min': 80, 'max': 100, 'remark': 'Excellent'},
                'B2': {'min': 70, 'max': 79, 'remark': 'Very Good'},
                'B3': {'min': 65, 'max': 69, 'remark': 'Good'},
                'C4': {'min': 60, 'max': 64, 'remark': 'Credit'},
                'C5': {'min': 55, 'max': 59, 'remark': 'Credit'},
                'C6': {'min': 50, 'max': 54, 'remark': 'Credit'},
                'D7': {'min': 45, 'max': 49, 'remark': 'Pass'},
                'E8': {'min': 40, 'max': 44, 'remark': 'Pass'},
                'F9': {'min': 0, 'max': 39, 'remark': 'Fail'}
            },
            'alphabetic': {
                'A': {'min': 90, 'max': 100, 'remark': 'Excellent'},
                'B': {'min': 80, 'max': 89, 'remark': 'Very Good'},
                'C': {'min': 70, 'max': 79, 'remark': 'Good'},
                'D': {'min': 60, 'max': 69, 'remark': 'Satisfactory'},
                'E': {'min': 50, 'max': 59, 'remark': 'Pass'},
                'F': {'min': 0, 'max': 49, 'remark': 'Fail'}
            },
            'numeric': {
                '1': {'min': 80, 'max': 100, 'remark': 'Excellent'},
                '2': {'min': 70, 'max': 79, 'remark': 'Very Good'},
                '3': {'min': 60, 'max': 69, 'remark': 'Good'},
                '4': {'min': 50, 'max': 59, 'remark': 'Satisfactory'},
                '5': {'min': 0, 'max': 49, 'remark': 'Needs Improvement'}
            },
            'percentage': {
                '90-100': {'min': 90, 'max': 100, 'remark': 'Excellent'},
                '80-89': {'min': 80, 'max': 89, 'remark': 'Very Good'},
                '70-79': {'min': 70, 'max': 79, 'remark': 'Good'},
                '60-69': {'min': 60, 'max': 69, 'remark': 'Satisfactory'},
                '50-59': {'min': 50, 'max': 59, 'remark': 'Pass'},
                '0-49': {'min': 0, 'max': 49, 'remark': 'Fail'}
            }
        }
        return defaults.get(grading_type, defaults['waec'])


class HeroBannerForm(forms.ModelForm):
    """Form for managing hero banners"""
    class Meta:
        model = HeroBanner
        fields = [
            'title', 'subtitle', 'description', 'image_url', 'background_image',
            'primary_button_text', 'primary_button_url', 'secondary_button_text',
            'secondary_button_url', 'show_stats', 'show_floating_cards',
            'background_color', 'gradient_colors', 'is_active'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Hero banner title'
            }),
            'subtitle': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Hero banner subtitle'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Hero banner description',
                'rows': 4
            }),
            'image_url': forms.URLInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'https://example.com/image.jpg (optional)'
            }),
            'background_image': forms.FileInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'accept': 'image/*'
            }),
            'primary_button_text': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Primary button text'
            }),
            'primary_button_url': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': '/register/school-admin/'
            }),
            'secondary_button_text': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Secondary button text'
            }),
            'secondary_button_url': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': '/login/'
            }),
            'background_color': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
            'gradient_colors': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'from-blue-900 via-blue-800 to-indigo-900'
            }),
            'show_stats': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500'
            }),
            'show_floating_cards': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500'
            }),
        }

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Ensure only one banner is active at a time
        if instance.is_active:
            HeroBanner.objects.filter(is_active=True).exclude(pk=instance.pk).update(is_active=False)

        if commit:
            instance.save()
        return instance


class SystemReportCardTemplateForm(forms.ModelForm):
    """Form for managing system-wide report card templates"""

    # Additional fields for template configuration
    layout_config_json = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        help_text="JSON representation of layout configuration"
    )

    fields_config_json = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        help_text="JSON representation of fields configuration"
    )

    class Meta:
        model = SystemReportCardTemplate
        fields = ['name', 'description', 'template_type', 'design_style', 'color_scheme', 'layout_config_json', 'fields_config_json']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Template name (e.g., Primary School Standard)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Optional description',
                'rows': 3
            }),
            'template_type': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
            'design_style': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
            'color_scheme': forms.Select(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Pre-populate configuration for editing
            import json
            self.fields['layout_config_json'].initial = json.dumps(self.instance.layout_config)
            self.fields['fields_config_json'].initial = json.dumps(self.instance.fields_config)

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Process layout configuration
        if self.cleaned_data.get('layout_config_json'):
            import json
            try:
                instance.layout_config = json.loads(self.cleaned_data['layout_config_json'])
            except json.JSONDecodeError:
                instance.layout_config = self.get_default_layout_config(instance.template_type, instance.design_style)
        else:
            instance.layout_config = self.get_default_layout_config(instance.template_type, instance.design_style)

        # Process fields configuration
        if self.cleaned_data.get('fields_config_json'):
            import json
            try:
                instance.fields_config = json.loads(self.cleaned_data['fields_config_json'])
            except json.JSONDecodeError:
                instance.fields_config = self.get_default_fields_config(instance.template_type)
        else:
            instance.fields_config = self.get_default_fields_config(instance.template_type)

        if commit:
            instance.save()
        return instance

    def get_default_layout_config(self, template_type, design_style):
        """Get default layout configuration"""
        return {
            'page_format': 'A4_portrait',
            'design_style': design_style,
            'header': {
                'show_logo': True,
                'show_school_name': True,
                'show_motto': True,
                'show_address': True,
                'show_contact': True,
                'logo_position': 'left',
                'header_style': 'professional'
            },
            'student_info': {
                'show_photo': True,
                'photo_position': 'top_right',
                'photo_size': 'medium',
                'show_student_id': True,
                'show_admission_number': True,
                'show_class': True,
                'show_term': True,
                'show_academic_year': True,
                'show_school_reopens': True,
                'layout_style': 'two_column'
            },
            'grades': {
                'show_subject_scores': True,
                'show_class_score': True,
                'show_exams_score': True,
                'show_total_score': True,
                'show_grade': True,
                'show_subject_position': True,
                'show_overall_position': True,
                'show_remarks': True,
                'table_style': 'bordered_professional',
                'show_class_average': True
            },
            'summary': {
                'show_total_marks': True,
                'show_average_marks': True,
                'show_overall_position': True,
                'show_class_size': True,
                'show_promoted_to': True,
                'show_attendance': True
            },
            'footer': {
                'show_teacher_signature': True,
                'show_head_signature': True,
                'show_date': True,
                'signature_layout': 'three_column'
            }
        }

    def get_default_fields_config(self, template_type):
        """Get default fields configuration"""
        base_config = {
            'attendance': True,
            'remarks': True,
            'next_term_begins': True,
            'promoted_to_class': True,
            'conduct_assessment': True,
        }

        if template_type == 'primary':
            base_config.update({
                'subjects': ['English Language', 'Mathematics', 'Science', 'Social Studies', 'ICT', 'French', 'Ghanaian Language', 'Creative Arts'],
                'assessment_types': ['Class Score', 'Exams Score', 'Total Score'],
                'show_skills_assessment': True,
            })
        elif template_type == 'jhs':
            base_config.update({
                'subjects': ['English Language', 'Mathematics', 'Integrated Science', 'Social Studies', 'ICT', 'French', 'Ghanaian Language', 'Creative Arts'],
                'assessment_types': ['Class Score', 'Exams Score', 'Total Score'],
                'show_career_guidance': True,
            })
        elif template_type == 'shs':
            base_config.update({
                'subjects': ['Core Mathematics', 'English Language', 'Physics', 'Chemistry', 'Biology', 'Geography', 'Economics', 'Government'],
                'assessment_types': ['Class Score', 'Exams Score', 'Total Score'],
                'show_programme_info': True,
                'show_credit_hours': True,
            })

        return base_config


class SystemTermForm(forms.ModelForm):
    """Form for managing system terms"""
    class Meta:
        model = SystemTerm
        fields = ['name', 'short_name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Term name (e.g., First Term)'
            }),
            'short_name': forms.TextInput(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Short name (e.g., Term 1)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                'placeholder': 'Optional description',
                'rows': 3
            }),
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            name = name.strip().title()
            # Check for duplicate names (excluding current instance if editing)
            existing = SystemTerm.objects.filter(name__iexact=name)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('A term with this name already exists.')
        return name

    def clean_short_name(self):
        short_name = self.cleaned_data.get('short_name')
        if short_name:
            short_name = short_name.strip()
            if len(short_name) > 10:
                raise forms.ValidationError('Short name must be 10 characters or less.')
            # Check for duplicate short names (excluding current instance if editing)
            existing = SystemTerm.objects.filter(short_name__iexact=short_name)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('A term with this short name already exists.')
        return short_name


# School Admin Configuration Forms
class GradingScaleForm(forms.ModelForm):
    class Meta:
        model = SchoolGradingScale
        fields = ['grading_type']
        widgets = {
            'grading_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
            })
        }

class GradingSetupForm(forms.Form):
    """Simple form for grading type selection during setup"""
    GRADING_CHOICES = [
        ('waec', 'WAEC Style (A1-F9) - For SHS'),
        ('ges_bece', 'GES BECE (1-9) - For Basic Schools'),
        ('alphabetic', 'Alphabetic (A-F)'),
        ('numeric', 'Numeric (1-5)'),
        ('percentage', 'Percentage (0-100)'),
        ('custom', 'Custom'),
    ]

    grading_type = forms.ChoiceField(
        choices=GRADING_CHOICES,
        initial='waec',
        widget=forms.Select(attrs={
            'class': 'w-full pl-10 pr-8 py-2.5 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/40 transition-all duration-300 backdrop-blur-sm text-sm cursor-pointer grading-select',
            'style': 'color: white !important;'
        })
    )


class GradeRangeForm(forms.ModelForm):
    class Meta:
        model = SchoolGradeRange
        fields = ['grade', 'min_score', 'max_score', 'remark', 'order']
        widgets = {
            'grade': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'A1, B, 1, etc.'
            }),
            'min_score': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'max_score': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'remark': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Excellent, Good, etc.'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': '0'
            })
        }


class GradingFieldForm(forms.ModelForm):
    class Meta:
        model = SchoolGradingField
        fields = ['name', 'code', 'percentage', 'max_score', 'order']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'Classwork, Test, Exam, etc.'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'placeholder': 'classwork, test, exam, etc.'
            }),
            'percentage': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'max_score': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'step': '0.01',
                'min': '1'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500',
                'min': '0'
            })
        }

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            # Convert to lowercase and replace spaces with underscores
            code = code.lower().replace(' ', '_')
        return code
