{% extends 'base.html' %}
{% load static %}

{% block title %}Student Management - {{ class_obj.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-slate-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-6">
                <div>
                    <h1 class="text-3xl font-bold text-slate-900">Student Management</h1>
                    <p class="text-slate-600 mt-1">{{ class_obj.name }} - {{ current_term }} {{ academic_year }}</p>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="save-all-btn" class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors">
                        <i data-lucide="save" class="w-5 h-5 mr-2 inline"></i>
                        Save All Changes
                    </button>
                    <a href="{% url 'teacher_dashboard' %}" class="px-6 py-3 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-2 inline"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Progress Summary -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-slate-900 mb-2">Completion Progress</h3>
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <i data-lucide="check" class="w-2 h-2 text-white"></i>
                            </div>
                            <span class="text-sm text-slate-600">Complete: <span id="complete-count" class="font-semibold">0</span></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-yellow-400 rounded-full"></div>
                            <span class="text-sm text-slate-600">Partial: <span id="partial-count" class="font-semibold">0</span></span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-slate-200 rounded-full"></div>
                            <span class="text-sm text-slate-600">Pending: <span id="pending-count" class="font-semibold">0</span></span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-slate-900" id="completion-percentage">0%</div>
                    <div class="text-sm text-slate-500">Overall Progress</div>
                </div>
            </div>
            <div class="mt-4">
                <div class="w-full bg-slate-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: 0%" id="progress-bar"></div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Student Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
                    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                        <h2 class="text-lg font-semibold text-white flex items-center">
                            <i data-lucide="users" class="w-5 h-5 mr-2"></i>
                            Students ({{ student_results|length }})
                        </h2>
                        <div class="mt-2 text-xs text-blue-100">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-green-400 rounded-full flex items-center justify-center">
                                        <i data-lucide="check" class="w-1.5 h-1.5 text-white"></i>
                                    </div>
                                    <span>Complete</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-yellow-300 rounded-full"></div>
                                    <span>Partial</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <div class="w-3 h-3 bg-slate-300 rounded-full"></div>
                                    <span>Pending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 space-y-2 max-h-96 overflow-y-auto">
                        {% for student_result in student_results %}
                        <div class="student-item p-3 rounded-xl border border-slate-200 hover:bg-blue-50 cursor-pointer transition-colors"
                             data-student-id="{{ student_result.student.id }}"
                             onclick="loadStudentData({{ student_result.student.id }})">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-bold">{{ student_result.student.first_name.0 }}{{ student_result.student.last_name.0 }}</span>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-slate-900">{{ student_result.student.first_name }} {{ student_result.student.last_name }}</p>
                                    <p class="text-xs text-slate-500">{{ student_result.student.student_id }}</p>
                                </div>
                                <div class="completion-indicator w-6 h-6 rounded-full bg-slate-200 flex items-center justify-center" id="indicator-{{ student_result.student.id }}">
                                    <i data-lucide="check" class="w-3 h-3 text-white hidden" id="check-{{ student_result.student.id }}"></i>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Global Settings -->
                <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden mt-6">
                    <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-4">
                        <h2 class="text-lg font-semibold text-white flex items-center">
                            <i data-lucide="settings" class="w-5 h-5 mr-2"></i>
                            Global Settings
                        </h2>
                    </div>
                    <div class="p-4 space-y-4">
                        <!-- Total School Days -->
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">Total School Days</label>
                            <input type="number" id="global-total-days" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., 55">
                        </div>
                        <button onclick="applyGlobalDays()" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                            Apply to All Students
                        </button>

                        <!-- Global Promotion -->
                        <div class="border-t border-slate-200 pt-4">
                            <div class="flex items-center space-x-2 mb-3">
                                <input type="checkbox" id="global-promotion-enabled" class="w-4 h-4 text-purple-600 border-slate-300 rounded focus:ring-purple-500">
                                <label for="global-promotion-enabled" class="text-sm font-medium text-slate-700">Promote all students to:</label>
                            </div>
                            <input type="text" id="global-promotion-class" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="e.g., BASIC 2" disabled>
                            <button onclick="applyGlobalPromotion()" class="w-full mt-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors" disabled id="apply-promotion-btn">
                                Apply to All Students
                            </button>
                        </div>

                        <!-- Save All Changes -->
                        <div class="border-t border-slate-200 pt-4">
                            <button onclick="saveAllChanges()" class="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                                <i data-lucide="save" class="w-5 h-5 mr-2 inline"></i>
                                Save All Changes
                            </button>
                            <p class="text-xs text-slate-500 mt-2 text-center">Save all student data at once</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student Details Form -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
                    <div class="bg-gradient-to-r from-green-600 to-teal-600 px-6 py-4">
                        <h2 class="text-lg font-semibold text-white flex items-center">
                            <i data-lucide="user-pen" class="w-5 h-5 mr-2"></i>
                            <span id="current-student-name">Select a student to edit</span>
                        </h2>
                    </div>
                    
                    <div id="student-form-container" class="p-6 hidden">
                        <form id="student-form" class="space-y-6">
                            {% csrf_token %}
                            <input type="hidden" id="current-student-id" name="student_id">
                            
                            <!-- Attendance Section -->
                            <div class="bg-blue-50 rounded-xl p-6">
                                <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                                    <i data-lucide="calendar-check" class="w-5 h-5 mr-2"></i>
                                    Attendance
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Days Present</label>
                                        <input type="number" id="attendance-present" name="attendance_present" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" min="0">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Total Days (Set in Global Settings)</label>
                                        <input type="number" id="attendance-total" name="attendance_total" class="w-full px-3 py-2 border border-slate-300 rounded-lg bg-slate-100 text-slate-600" readonly>
                                        <p class="text-xs text-slate-500 mt-1">Use Global Settings to set total days for all students</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Comments Section -->
                            <div class="bg-green-50 rounded-xl p-6">
                                <h3 class="text-lg font-semibold text-green-900 mb-4 flex items-center">
                                    <i data-lucide="message-circle" class="w-5 h-5 mr-2"></i>
                                    Student Assessment
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Conduct</label>
                                        <input type="text" id="conduct-comment" name="conduct_comment" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="e.g., Respectful and obedient">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Attitude</label>
                                        <input type="text" id="attitude-comment" name="attitude_comment" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="e.g., Hardworking">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-slate-700 mb-2">Interest</label>
                                        <input type="text" id="interest-comment" name="interest_comment" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="e.g., Shows keen interest in studies">
                                    </div>
                                    <div>
                                        <div class="flex items-center space-x-2 mb-2">
                                            <input type="checkbox" id="custom-promotion" name="custom_promotion" class="w-4 h-4 text-green-600 border-slate-300 rounded focus:ring-green-500">
                                            <label for="custom-promotion" class="text-sm font-medium text-slate-700">Custom promotion (override global setting)</label>
                                        </div>
                                        <input type="text" id="promoted-to" name="promoted_to" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-slate-100" placeholder="Uses global promotion setting" readonly>
                                    </div>
                                </div>
                            </div>

                            <!-- Teacher Comment Section -->
                            <div class="bg-purple-50 rounded-xl p-6">
                                <h3 class="text-lg font-semibold text-purple-900 mb-4 flex items-center">
                                    <i data-lucide="edit-3" class="w-5 h-5 mr-2"></i>
                                    Teacher's Comment
                                </h3>
                                <textarea id="teacher-comment" name="teacher_comment" rows="4" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" placeholder="Enter your overall comment for this student's terminal report..."></textarea>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                                <button type="button" onclick="saveCurrentStudent()" class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
                                    <i data-lucide="save" class="w-5 h-5 mr-2 inline"></i>
                                    Save Student
                                </button>
                                <button type="button" onclick="clearForm()" class="px-6 py-3 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors">
                                    <i data-lucide="x" class="w-5 h-5 mr-2 inline"></i>
                                    Clear Form
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Empty State -->
                    <div id="empty-state" class="p-12 text-center">
                        <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="user-plus" class="w-12 h-12 text-slate-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-slate-900 mb-2">Select a Student</h3>
                        <p class="text-slate-600">Choose a student from the sidebar to edit their attendance and comments.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Student data storage
let studentData = {};
let currentStudentId = null;

// Initialize student data from server
{% for student_result in student_results %}
studentData[{{ student_result.student.id }}] = {
    id: {{ student_result.student.id }},
    name: "{{ student_result.student.first_name }} {{ student_result.student.last_name }}",
    attendance_present: {{ student_result.result.attendance_present|default:0 }},
    attendance_total: {{ student_result.result.attendance_total|default:0 }},
    conduct_comment: "{{ student_result.result.conduct_comment|default:'' }}",
    attitude_comment: "{{ student_result.result.attitude_comment|default:'' }}",
    interest_comment: "{{ student_result.result.interest_comment|default:'' }}",
    promoted_to: "{{ student_result.result.promoted_to|default:'' }}",
    teacher_comment: "{{ student_result.result.teacher_comment|default:'' }}",
    customPromotion: {% if student_result.result.promoted_to %}true{% else %}false{% endif %},
    isDirty: false
};
{% endfor %}

function loadStudentData(studentId) {
    // Save current student data if any
    if (currentStudentId && currentStudentId !== studentId) {
        saveCurrentStudentData();
    }
    
    currentStudentId = studentId;
    const student = studentData[studentId];
    
    // Update UI
    document.getElementById('current-student-name').textContent = student.name;
    document.getElementById('current-student-id').value = studentId;
    
    // Populate form
    document.getElementById('attendance-present').value = student.attendance_present;
    document.getElementById('attendance-total').value = student.attendance_total;
    document.getElementById('conduct-comment').value = student.conduct_comment;
    document.getElementById('attitude-comment').value = student.attitude_comment;
    document.getElementById('interest-comment').value = student.interest_comment;
    document.getElementById('promoted-to').value = student.promoted_to;
    document.getElementById('teacher-comment').value = student.teacher_comment;

    // Handle custom promotion checkbox
    const customPromotionCheckbox = document.getElementById('custom-promotion');
    const promotedToInput = document.getElementById('promoted-to');

    customPromotionCheckbox.checked = student.customPromotion;
    if (student.customPromotion) {
        promotedToInput.readOnly = false;
        promotedToInput.classList.remove('bg-slate-100');
        promotedToInput.placeholder = 'e.g., BASIC 4';
    } else {
        promotedToInput.readOnly = true;
        promotedToInput.classList.add('bg-slate-100');
        promotedToInput.placeholder = 'Uses global promotion setting';
    }
    
    // Show form, hide empty state
    document.getElementById('student-form-container').classList.remove('hidden');
    document.getElementById('empty-state').classList.add('hidden');
    
    // Update sidebar selection
    document.querySelectorAll('.student-item').forEach(item => {
        item.classList.remove('bg-blue-100', 'border-blue-300');
    });
    document.querySelector(`[data-student-id="${studentId}"]`).classList.add('bg-blue-100', 'border-blue-300');
    
    updateCompletionIndicator(studentId);
}

function saveCurrentStudentData() {
    if (!currentStudentId) return;

    const student = studentData[currentStudentId];
    student.attendance_present = parseInt(document.getElementById('attendance-present').value) || 0;
    student.attendance_total = parseInt(document.getElementById('attendance-total').value) || 0;
    student.conduct_comment = document.getElementById('conduct-comment').value;
    student.attitude_comment = document.getElementById('attitude-comment').value;
    student.interest_comment = document.getElementById('interest-comment').value;
    student.promoted_to = document.getElementById('promoted-to').value;
    student.teacher_comment = document.getElementById('teacher-comment').value;
    student.customPromotion = document.getElementById('custom-promotion').checked;
    student.isDirty = true;

    updateCompletionIndicator(currentStudentId);
    updateProgressSummary();
}

function updateCompletionIndicator(studentId) {
    const student = studentData[studentId];
    const indicator = document.getElementById(`indicator-${studentId}`);
    const checkIcon = document.getElementById(`check-${studentId}`);
    const studentItem = document.querySelector(`[data-student-id="${studentId}"]`);

    // Check if student data is complete
    const isComplete = student.attendance_total > 0 &&
                      student.conduct_comment.trim() !== '' &&
                      student.attitude_comment.trim() !== '' &&
                      student.interest_comment.trim() !== '' &&
                      student.promoted_to.trim() !== '';

    // Update completion indicator
    if (isComplete) {
        indicator.classList.remove('bg-slate-200', 'bg-yellow-400');
        indicator.classList.add('bg-green-500');
        checkIcon.classList.remove('hidden');
    } else {
        // Check if partially complete (some fields filled)
        const isPartial = student.attendance_total > 0 ||
                         student.conduct_comment.trim() !== '' ||
                         student.attitude_comment.trim() !== '' ||
                         student.interest_comment.trim() !== '' ||
                         student.promoted_to.trim() !== '';

        if (isPartial) {
            indicator.classList.remove('bg-slate-200', 'bg-green-500');
            indicator.classList.add('bg-yellow-400');
        } else {
            indicator.classList.remove('bg-green-500', 'bg-yellow-400');
            indicator.classList.add('bg-slate-200');
        }
        checkIcon.classList.add('hidden');
    }

    // Update dirty indicator
    if (student.isDirty) {
        studentItem.classList.add('border-l-4', 'border-l-orange-400');
        // Add unsaved changes indicator
        let dirtyIndicator = studentItem.querySelector('.dirty-indicator');
        if (!dirtyIndicator) {
            dirtyIndicator = document.createElement('div');
            dirtyIndicator.className = 'dirty-indicator absolute top-2 right-2 w-2 h-2 bg-orange-400 rounded-full';
            studentItem.style.position = 'relative';
            studentItem.appendChild(dirtyIndicator);
        }
    } else {
        studentItem.classList.remove('border-l-4', 'border-l-orange-400');
        const dirtyIndicator = studentItem.querySelector('.dirty-indicator');
        if (dirtyIndicator) {
            dirtyIndicator.remove();
        }
    }
}

function saveCurrentStudent() {
    if (!currentStudentId) return;

    // Show loading state
    const saveBtn = document.querySelector('button[onclick="saveCurrentStudent()"]');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 mr-2 inline animate-spin"></i>Saving...';
    saveBtn.disabled = true;

    saveCurrentStudentData();

    // Send AJAX request to save individual student
    const student = studentData[currentStudentId];
    const formData = new FormData();
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    formData.append('student_id', currentStudentId);
    formData.append('attendance_present', student.attendance_present);
    formData.append('attendance_total', student.attendance_total);
    formData.append('conduct_comment', student.conduct_comment);
    formData.append('attitude_comment', student.attitude_comment);
    formData.append('interest_comment', student.interest_comment);
    formData.append('promoted_to', student.promoted_to);
    formData.append('teacher_comment', student.teacher_comment);
    formData.append('custom_promotion', student.customPromotion);

    console.log('Saving student data:', {
        student_id: currentStudentId,
        attendance_present: student.attendance_present,
        attendance_total: student.attendance_total,
        conduct_comment: student.conduct_comment,
        attitude_comment: student.attitude_comment,
        interest_comment: student.interest_comment,
        promoted_to: student.promoted_to,
        teacher_comment: student.teacher_comment,
        custom_promotion: student.customPromotion
    });

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            student.isDirty = false;
            updateCompletionIndicator(currentStudentId);
            updateProgressSummary();
            showNotification(`${student.name}'s data saved successfully!`, 'success');
        } else {
            showNotification(`Error saving ${student.name}'s data: ${data.error || 'Unknown error'}`, 'error');
        }
    })
    .catch(error => {
        console.error('Save error:', error);
        showNotification(`Error saving ${student.name}'s data: ${error.message || 'Please try again.'}`, 'error');
    })
    .finally(() => {
        // Restore button state
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
        lucide.createIcons();
    });
}

function applyGlobalDays() {
    const totalDays = parseInt(document.getElementById('global-total-days').value);
    if (!totalDays || totalDays <= 0) {
        showNotification('Please enter a valid number of total days', 'error');
        return;
    }

    // Apply to all students
    Object.keys(studentData).forEach(studentId => {
        studentData[studentId].attendance_total = totalDays;
        studentData[studentId].isDirty = true;
        updateCompletionIndicator(studentId);
    });

    // Update current form if showing
    if (currentStudentId) {
        document.getElementById('attendance-total').value = totalDays;
    }

    updateProgressSummary();
    showNotification(`Applied ${totalDays} total days to all students`, 'success');
}

function applyGlobalPromotion() {
    const promotionClass = document.getElementById('global-promotion-class').value.trim();
    if (!promotionClass) {
        showNotification('Please enter a promotion class', 'error');
        return;
    }

    // Apply to all students who don't have custom promotion
    let appliedCount = 0;
    Object.keys(studentData).forEach(studentId => {
        if (!studentData[studentId].customPromotion) {
            studentData[studentId].promoted_to = promotionClass;
            studentData[studentId].isDirty = true;
            appliedCount++;
            updateCompletionIndicator(studentId);
        }
    });

    // Update current form if showing and not using custom promotion
    if (currentStudentId && !document.getElementById('custom-promotion').checked) {
        document.getElementById('promoted-to').value = promotionClass;
    }

    updateProgressSummary();
    showNotification(`Applied promotion to ${appliedCount} students`, 'success');
}

function saveAllChanges() {
    // Save current student data first
    if (currentStudentId) {
        saveCurrentStudentData();
    }

    // Count dirty students
    const dirtyStudents = Object.values(studentData).filter(student => student.isDirty);
    if (dirtyStudents.length === 0) {
        showNotification('No changes to save', 'error');
        return;
    }

    // Show loading state
    const saveAllBtn = document.querySelector('button[onclick="saveAllChanges()"]');
    const originalText = saveAllBtn.innerHTML;
    saveAllBtn.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 mr-2 inline animate-spin"></i>Saving all changes...';
    saveAllBtn.disabled = true;

    // Save all dirty students
    let savedCount = 0;
    let errorCount = 0;

    const savePromises = dirtyStudents.map(student => {
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        formData.append('student_id', student.id);
        formData.append('attendance_present', student.attendance_present);
        formData.append('attendance_total', student.attendance_total);
        formData.append('conduct_comment', student.conduct_comment);
        formData.append('attitude_comment', student.attitude_comment);
        formData.append('interest_comment', student.interest_comment);
        formData.append('promoted_to', student.promoted_to);
        formData.append('teacher_comment', student.teacher_comment);
        formData.append('custom_promotion', student.customPromotion);

        return fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                student.isDirty = false;
                savedCount++;
            } else {
                errorCount++;
                console.error(`Error saving ${student.name}:`, data.error);
            }
        })
        .catch(error => {
            errorCount++;
            console.error(`Error saving ${student.name}:`, error);
        });
    });

    Promise.all(savePromises).then(() => {
        // Restore button state
        saveAllBtn.innerHTML = originalText;
        saveAllBtn.disabled = false;
        lucide.createIcons();

        // Show results
        if (errorCount === 0) {
            showNotification(`Successfully saved data for all ${savedCount} students!`, 'success');
        } else if (savedCount > 0) {
            showNotification(`Saved ${savedCount} students, ${errorCount} failed. Please check individual students.`, 'error');
        } else {
            showNotification(`Failed to save data for ${errorCount} students. Please try again.`, 'error');
        }
    });
}

function clearForm() {
    if (currentStudentId) {
        // Reset to original data
        const student = studentData[currentStudentId];
        loadStudentData(currentStudentId);
    }
}

function showNotification(message, type) {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification-toast fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white flex items-center space-x-3`;

    // Add icon
    const icon = document.createElement('i');
    icon.setAttribute('data-lucide', type === 'success' ? 'check-circle' : 'alert-circle');
    icon.className = 'w-5 h-5';

    // Add message
    const messageSpan = document.createElement('span');
    messageSpan.textContent = message;

    notification.appendChild(icon);
    notification.appendChild(messageSpan);

    document.body.appendChild(notification);

    // Initialize the icon
    lucide.createIcons();

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 10);

    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

// Update progress summary
function updateProgressSummary() {
    const totalStudents = Object.keys(studentData).length;
    let completeCount = 0;
    let partialCount = 0;
    let pendingCount = 0;

    Object.values(studentData).forEach(student => {
        const isComplete = student.attendance_total > 0 &&
                          student.conduct_comment.trim() !== '' &&
                          student.attitude_comment.trim() !== '' &&
                          student.interest_comment.trim() !== '' &&
                          student.promoted_to.trim() !== '';

        const isPartial = student.attendance_total > 0 ||
                         student.conduct_comment.trim() !== '' ||
                         student.attitude_comment.trim() !== '' ||
                         student.interest_comment.trim() !== '' ||
                         student.promoted_to.trim() !== '';

        if (isComplete) {
            completeCount++;
        } else if (isPartial) {
            partialCount++;
        } else {
            pendingCount++;
        }
    });

    // Update counts
    document.getElementById('complete-count').textContent = completeCount;
    document.getElementById('partial-count').textContent = partialCount;
    document.getElementById('pending-count').textContent = pendingCount;

    // Update percentage and progress bar
    const percentage = totalStudents > 0 ? Math.round((completeCount / totalStudents) * 100) : 0;
    document.getElementById('completion-percentage').textContent = percentage + '%';
    document.getElementById('progress-bar').style.width = percentage + '%';
}

// Initialize completion indicators for all students
function initializeCompletionIndicators() {
    Object.keys(studentData).forEach(studentId => {
        updateCompletionIndicator(studentId);
    });
    updateProgressSummary();
}

// Initialize Lucide icons and event handlers
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();

    // Initialize completion indicators
    initializeCompletionIndicators();

    // Global promotion checkbox handler
    const globalPromotionCheckbox = document.getElementById('global-promotion-enabled');
    const globalPromotionInput = document.getElementById('global-promotion-class');
    const applyPromotionBtn = document.getElementById('apply-promotion-btn');

    globalPromotionCheckbox.addEventListener('change', function() {
        globalPromotionInput.disabled = !this.checked;
        applyPromotionBtn.disabled = !this.checked;

        if (this.checked) {
            globalPromotionInput.classList.remove('bg-slate-100');
            globalPromotionInput.focus();
        } else {
            globalPromotionInput.classList.add('bg-slate-100');
        }
    });

    // Custom promotion checkbox handler
    const customPromotionCheckbox = document.getElementById('custom-promotion');
    const promotedToInput = document.getElementById('promoted-to');

    customPromotionCheckbox.addEventListener('change', function() {
        if (this.checked) {
            promotedToInput.readOnly = false;
            promotedToInput.classList.remove('bg-slate-100');
            promotedToInput.placeholder = 'e.g., BASIC 4';
            promotedToInput.focus();

            if (currentStudentId) {
                studentData[currentStudentId].customPromotion = true;
            }
        } else {
            promotedToInput.readOnly = true;
            promotedToInput.classList.add('bg-slate-100');
            promotedToInput.placeholder = 'Uses global promotion setting';

            // Reset to global promotion if available
            const globalPromotion = document.getElementById('global-promotion-class').value;
            if (globalPromotion && document.getElementById('global-promotion-enabled').checked) {
                promotedToInput.value = globalPromotion;
            } else {
                promotedToInput.value = '';
            }

            if (currentStudentId) {
                studentData[currentStudentId].customPromotion = false;
                studentData[currentStudentId].promoted_to = promotedToInput.value;
            }
        }
    });
});
</script>
{% endblock %}
