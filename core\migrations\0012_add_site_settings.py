# Generated by Django 4.2.7 on 2025-06-24 20:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0011_student_id_card_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_host', models.CharField(default='smtp.gmail.com', help_text='SMTP server address', max_length=255)),
                ('email_port', models.IntegerField(default=587, help_text='SMTP port (usually 587 for TLS or 465 for SSL)')),
                ('email_host_user', models.EmailField(help_text='Email address for sending emails', max_length=254)),
                ('email_host_password', models.Char<PERSON>ield(help_text='Email password or app password', max_length=255)),
                ('email_use_tls', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Use TLS encryption')),
                ('email_use_ssl', models.<PERSON><PERSON>anField(default=False, help_text='Use SSL encryption')),
                ('site_name', models.CharField(default='Smart Terminal Report System', max_length=200)),
                ('support_email', models.EmailField(blank=True, help_text='Support contact email', max_length=254)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Site Settings',
                'verbose_name_plural': 'Site Settings',
            },
        ),
        migrations.RemoveField(
            model_name='schoolsettings',
            name='email_host',
        ),
        migrations.RemoveField(
            model_name='schoolsettings',
            name='email_host_password',
        ),
        migrations.RemoveField(
            model_name='schoolsettings',
            name='email_host_user',
        ),
        migrations.RemoveField(
            model_name='schoolsettings',
            name='email_port',
        ),
        migrations.RemoveField(
            model_name='schoolsettings',
            name='email_use_ssl',
        ),
        migrations.RemoveField(
            model_name='schoolsettings',
            name='email_use_tls',
        ),
    ]
