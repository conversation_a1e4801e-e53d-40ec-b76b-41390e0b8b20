{% extends 'base.html' %}
{% load static %}
{% load score_tags %}

{% block title %}Score Entry - {{ class_obj.name }} - {{ subject.name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Mobile-First Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
            <div class="flex flex-col space-y-3 py-4 sm:py-6">
                <!-- Top Row: Back Button and Title -->
                <div class="flex items-center space-x-3">
                    <a href="{% url 'teacher_class_subjects' class_obj.id %}"
                       class="text-gray-400 hover:text-gray-600 p-2 -ml-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i data-lucide="arrow-left" class="w-5 h-5 sm:w-6 sm:h-6"></i>
                    </a>
                    <div class="flex-1 min-w-0">
                        <h1 class="text-lg sm:text-2xl font-bold text-gray-900">Score Entry</h1>
                        <p class="text-xs sm:text-sm text-gray-600 truncate">
                            {{ class_obj.name }} - {{ subject.name }}
                        </p>
                        <p class="text-xs text-gray-500 sm:hidden">{{ current_term }} {{ academic_year }}</p>
                    </div>
                </div>

                <!-- Progress Section -->
                <div class="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <i data-lucide="check-circle" class="w-4 h-4 text-green-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ submitted_count }}/{{ total_students }} Submitted</p>
                            <p class="text-xs text-gray-600">{{ completion_percentage }}% Complete</p>
                        </div>
                    </div>
                    <div class="w-20 sm:w-32 bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full transition-all duration-300" style="width: {{ completion_percentage }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile-First Main Content -->
    <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
        <!-- Mobile-Optimized Statistics Cards -->
        <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8">
            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="users" class="w-3 h-3 sm:w-4 sm:h-4 text-blue-600"></i>
                    </div>
                    <div class="sm:ml-3">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Total Students</p>
                        <p class="text-base sm:text-lg font-semibold text-gray-900">{{ total_students }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-6 h-6 sm:w-8 sm:h-8 bg-green-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="check-circle" class="w-3 h-3 sm:w-4 sm:h-4 text-green-600"></i>
                    </div>
                    <div class="sm:ml-3">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Submitted</p>
                        <p class="text-base sm:text-lg font-semibold text-gray-900">{{ submitted_count }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-6 h-6 sm:w-8 sm:h-8 bg-yellow-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="edit" class="w-3 h-3 sm:w-4 sm:h-4 text-yellow-600"></i>
                    </div>
                    <div class="sm:ml-3">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Drafts</p>
                        <p class="text-base sm:text-lg font-semibold text-gray-900">{{ draft_count }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3 sm:p-4 lg:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <div class="w-6 h-6 sm:w-8 sm:h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-2 sm:mb-0">
                        <i data-lucide="percent" class="w-3 h-3 sm:w-4 sm:h-4 text-purple-600"></i>
                    </div>
                    <div class="sm:ml-3">
                        <p class="text-xs sm:text-sm font-medium text-gray-600">Completion</p>
                        <p class="text-base sm:text-lg font-semibold text-gray-900">{{ completion_percentage|floatformat:0 }}%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile-First Score Entry Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="px-3 sm:px-4 lg:px-6 py-3 sm:py-4 border-b border-gray-200">
                <div class="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:justify-between sm:items-center">
                    <h3 class="text-base sm:text-lg font-medium text-gray-900">
                        <i data-lucide="edit-3" class="w-4 h-4 inline mr-2 text-blue-600"></i>
                        Student Scores
                    </h3>
                    <div class="flex flex-wrap gap-2">
                        <button type="button" onclick="calculateTotals()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <i data-lucide="calculator" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"></i>
                            Calculate
                        </button>
                        <button type="button" onclick="toggleMobileView()"
                                class="sm:hidden inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-xs leading-4 font-medium rounded-lg text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors">
                            <i data-lucide="smartphone" class="w-3 h-3 mr-1"></i>
                            Mobile View
                        </button>
                    </div>
                </div>
            </div>

            <form method="post" id="scoreForm" class="p-3 sm:p-4 lg:p-6">
                {% csrf_token %}
                <input type="hidden" name="entry_mode" id="entry_mode_hidden" value="raw_scores">

                <!-- Mobile-Optimized Score Entry Mode Selection -->
                <div class="mb-4 sm:mb-6 p-3 sm:p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <h4 class="text-sm font-medium text-amber-900 mb-3 flex items-center">
                        <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                        Score Entry Mode for {{ subject.name }}
                    </h4>
                    <div class="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-1 lg:grid-cols-3 sm:gap-3 lg:gap-4">
                        <label class="flex items-start p-3 bg-white border border-amber-200 rounded-lg cursor-pointer hover:bg-amber-50 transition-colors active:scale-95">
                            <input type="radio" name="entry_mode" value="raw_scores" class="mt-1 mr-3 text-amber-600" checked>
                            <div class="flex-1">
                                <div class="font-medium text-gray-900 text-sm">Raw Scores</div>
                                <div class="text-xs text-gray-600 mt-1">Enter actual scores, system calculates percentages</div>
                            </div>
                        </label>
                        <label class="flex items-start p-3 bg-white border border-amber-200 rounded-lg cursor-pointer hover:bg-amber-50 transition-colors active:scale-95">
                            <input type="radio" name="entry_mode" value="converted_classwork" class="mt-1 mr-3 text-amber-600">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900 text-sm">Converted Classwork</div>
                                <div class="text-xs text-gray-600 mt-1">Classwork converted, only exam needs calculation</div>
                            </div>
                        </label>
                        <label class="flex items-start p-3 bg-white border border-amber-200 rounded-lg cursor-pointer hover:bg-amber-50 transition-colors active:scale-95">
                            <input type="radio" name="entry_mode" value="all_converted" class="mt-1 mr-3 text-amber-600">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900 text-sm">All Converted</div>
                                <div class="text-xs text-gray-600 mt-1">All scores converted, just add them up</div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Grading Fields Info -->
                <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Grading Configuration:</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-blue-800">
                        {% for field in grading_fields %}
                        <div>
                            <span class="font-medium">{{ field.name }}:</span> {{ field.percentage }}% (Max: {{ field.max_score }})
                        </div>
                        {% endfor %}
                    </div>
                    <div id="mode-explanation" class="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
                        <strong>Raw Scores Mode:</strong> Enter the actual scores students got. The system will convert them based on the percentages above.
                    </div>
                </div>
                
                <!-- Spreadsheet Table -->
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10">
                                    Student
                                </th>
                                {% for field in grading_fields %}
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {{ field.name }}
                                    <br>
                                    <span class="text-xs text-gray-400">({{ field.percentage }}%)</span>
                                </th>
                                {% endfor %}
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Total
                                </th>
                                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Grade
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for student in students %}
                            <tr class="hover:bg-gray-50" data-student-id="{{ student.id }}">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 sticky left-0 bg-white z-10">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-blue-600">{{ student.first_name.0 }}{{ student.last_name.0 }}</span>
                                        </div>
                                        {{ student.full_name }}
                                    </div>
                                </td>
                                {% for field in grading_fields %}
                                <td class="px-3 py-4 whitespace-nowrap text-center">
                                    <input type="number"
                                           name="score_{{ student.id }}_{{ field.code }}"
                                           min="0"
                                           max="{{ field.max_score }}"
                                           step="0.01"
                                           data-student-id="{{ student.id }}"
                                           data-field-code="{{ field.code }}"
                                           data-field-percentage="{{ field.percentage }}"
                                           data-max-score="{{ field.max_score }}"
                                           class="w-20 px-2 py-1 border border-gray-300 rounded text-center focus:outline-none focus:ring-1 focus:ring-blue-500"
                                           placeholder="0"
                                           value="{% get_student_score existing_scores student.id field.code %}">
                                </td>
                                {% endfor %}
                                <td class="px-3 py-4 whitespace-nowrap text-center">
                                    <span class="total-score font-semibold text-gray-900" data-student-id="{{ student.id }}">0.00</span>
                                </td>
                                <td class="px-3 py-4 whitespace-nowrap text-center">
                                    <span class="grade-display font-semibold" data-student-id="{{ student.id }}">-</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-4">
                        <button type="button" onclick="clearAllScores()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                            Clear All
                        </button>
                        <button type="button" onclick="fillSampleData()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="shuffle" class="w-4 h-4 mr-2"></i>
                            Sample Data
                        </button>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button type="submit" name="action" value="save_draft" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i data-lucide="save" class="w-4 h-4 mr-2"></i>
                            Save Draft
                        </button>
                        <button type="submit" name="action" value="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                            Submit Scores
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    lucide.createIcons();
    
    // Grade calculation logic - Use school's grading system
    const gradeScale = {{ school.get_grading_scale_json|safe }};
    
    function calculateTotals() {
        const entryMode = document.querySelector('input[name="entry_mode"]:checked').value;
        const students = document.querySelectorAll('[data-student-id]');

        students.forEach(row => {
            const studentId = row.dataset.studentId;
            if (!studentId) return;

            let totalWeightedScore = 0;
            const inputs = row.querySelectorAll('input[type="number"]');

            inputs.forEach(input => {
                const score = parseFloat(input.value) || 0;
                const fieldCode = input.dataset.fieldCode;
                const percentage = parseFloat(input.dataset.fieldPercentage) || 0;
                const maxScore = parseFloat(input.dataset.maxScore) || 100;

                let weightedScore = 0;

                if (entryMode === 'raw_scores') {
                    // Mode 1: Raw scores - calculate everything normally
                    const normalizedScore = (score / maxScore) * 100;
                    weightedScore = (normalizedScore * percentage) / 100;
                } else if (entryMode === 'converted_classwork') {
                    // Mode 2: Classwork already converted, exam needs calculation
                    if (fieldCode === 'CW' || fieldCode === 'classwork') {
                        // Classwork is already converted to percentage, just apply weight
                        weightedScore = score;
                    } else {
                        // Other fields (like exam) need full calculation
                        const normalizedScore = (score / maxScore) * 100;
                        weightedScore = (normalizedScore * percentage) / 100;
                    }
                } else if (entryMode === 'all_converted') {
                    // Mode 3: All scores already converted, just add them
                    weightedScore = score;
                }

                totalWeightedScore += weightedScore;
            });

            // Update total display
            const totalDisplay = row.querySelector('.total-score');
            if (totalDisplay) {
                totalDisplay.textContent = totalWeightedScore.toFixed(2);
            }

            // Calculate and update grade
            const gradeDisplay = row.querySelector('.grade-display');
            if (gradeDisplay) {
                const grade = calculateGrade(totalWeightedScore);
                gradeDisplay.textContent = grade;
                gradeDisplay.className = `grade-display font-semibold ${getGradeColor(grade)}`;
            }
        });
    }
    
    function calculateGrade(score) {
        for (const range of gradeScale) {
            if (score >= range.min && score <= range.max) {
                return range.grade;
            }
        }
        return 'F9';
    }
    
    function getGradeColor(grade) {
        // Handle GES BECE grades (1-9)
        if (grade === '1' || grade === 'A' || grade === 'A1') return 'text-green-600';
        if (grade === '2' || grade === '3' || grade.startsWith('B')) return 'text-blue-600';
        if (grade === '4' || grade === '5' || grade === '6' || grade.startsWith('C')) return 'text-yellow-600';
        if (grade === '7' || grade === '8' || grade.startsWith('D') || grade.startsWith('E')) return 'text-orange-600';
        return 'text-red-600'; // Grade 9, F, F9, etc.
    }
    
    function clearAllScores() {
        if (confirm('Are you sure you want to clear all scores?')) {
            document.querySelectorAll('input[type="number"]').forEach(input => {
                input.value = '';
            });
            calculateTotals();
        }
    }
    
    function fillSampleData() {
        if (confirm('Fill with sample data? This will overwrite existing scores.')) {
            document.querySelectorAll('input[type="number"]').forEach(input => {
                const maxScore = parseFloat(input.dataset.maxScore) || 100;
                const randomScore = Math.floor(Math.random() * (maxScore * 0.8)) + (maxScore * 0.2);
                input.value = randomScore.toFixed(2);
            });
            calculateTotals();
        }
    }
    
    // Handle entry mode changes
    function updateModeExplanation() {
        const entryMode = document.querySelector('input[name="entry_mode"]:checked').value;
        const explanation = document.getElementById('mode-explanation');
        const hiddenField = document.getElementById('entry_mode_hidden');

        // Update hidden field for form submission
        hiddenField.value = entryMode;

        if (entryMode === 'raw_scores') {
            explanation.innerHTML = '<strong>Raw Scores Mode:</strong> Enter the actual scores students got. The system will convert them based on the percentages above.';
        } else if (entryMode === 'converted_classwork') {
            explanation.innerHTML = '<strong>Converted Classwork Mode:</strong> Enter classwork as already converted percentages (e.g., 25 for 25%). Exam scores will be calculated normally.';
        } else if (entryMode === 'all_converted') {
            explanation.innerHTML = '<strong>All Converted Mode:</strong> Enter all scores as already converted percentages. The system will just add them up.';
        }

        // Recalculate totals when mode changes
        calculateTotals();
    }

    // Auto-calculate on input change
    document.addEventListener('input', function(e) {
        if (e.target.type === 'number') {
            calculateTotals();
        }
    });

    // Handle mode change
    document.addEventListener('change', function(e) {
        if (e.target.name === 'entry_mode') {
            updateModeExplanation();
        }
    });

    // Initial calculation
    document.addEventListener('DOMContentLoaded', function() {
        updateModeExplanation();
        calculateTotals();
    });
    
    // Form submission validation
    document.getElementById('scoreForm').addEventListener('submit', function(e) {
        const action = e.submitter.value;
        if (action === 'submit') {
            if (!confirm('Are you sure you want to submit these scores? This action cannot be undone.')) {
                e.preventDefault();
            }
        }
    });
</script>
{% endblock %}
