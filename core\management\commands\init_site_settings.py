"""
Management command to initialize site settings with current SMTP configuration.
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from core.models import SiteSettings


class Command(BaseCommand):
    help = 'Initialize site settings with current SMTP configuration from settings.py'

    def handle(self, *args, **options):
        # Get or create site settings
        site_settings, created = SiteSettings.objects.get_or_create(pk=1)
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created new site settings'))
        else:
            self.stdout.write(self.style.WARNING('Site settings already exist, updating...'))
        
        # Update with current Django settings
        site_settings.email_host = getattr(settings, 'EMAIL_HOST', 'smtp.gmail.com')
        site_settings.email_port = getattr(settings, 'EMAIL_PORT', 587)
        site_settings.email_host_user = getattr(settings, 'EMAIL_HOST_USER', '')
        site_settings.email_host_password = getattr(settings, 'EMAIL_HOST_PASSWORD', '')
        site_settings.email_use_tls = getattr(settings, 'EMAIL_USE_TLS', True)
        site_settings.email_use_ssl = getattr(settings, 'EMAIL_USE_SSL', False)
        
        site_settings.save()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Site settings initialized:\n'
                f'  SMTP Host: {site_settings.email_host}\n'
                f'  SMTP Port: {site_settings.email_port}\n'
                f'  Email User: {site_settings.email_host_user}\n'
                f'  Use TLS: {site_settings.email_use_tls}\n'
                f'  Use SSL: {site_settings.email_use_ssl}'
            )
        )
        
        self.stdout.write(
            self.style.WARNING(
                '\nIMPORTANT: SMTP configuration has been moved to Site Settings in Django Admin.\n'
                'School admins can no longer configure SMTP - only site admins can do this.\n'
                'Schools will use centralized SMTP with their own branding in emails.'
            )
        )
