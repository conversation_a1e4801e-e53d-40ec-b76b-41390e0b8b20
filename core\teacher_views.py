from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.db import transaction, models
from django.utils import timezone
from .models import *
from .forms import *
from .decorators import teacher_required, role_required


def recalculate_subject_positions(subject, class_obj, term, academic_year):
    """Recalculate positions for all students in a subject"""
    scores = Score.objects.filter(
        subject=subject,
        student__current_class=class_obj,
        term=term,
        academic_year=academic_year,
        is_submitted=True
    ).order_by('-total_score')

    for position, score in enumerate(scores, 1):
        score.position = position
        score.save(update_fields=['position'])


def recalculate_class_positions(class_obj, term, academic_year):
    """Recalculate overall class positions for all students"""
    # Get all term results for this class
    results = StudentTermResult.objects.filter(
        student__current_class=class_obj,
        term=term,
        academic_year=academic_year
    ).order_by('-average_score', '-total_score')

    for position, result in enumerate(results, 1):
        result.position = position
        result.save(update_fields=['position'])


@teacher_required
def teacher_dashboard(request):
    """Teacher dashboard showing assigned classes and subjects"""
    try:
        profile = request.user.profile
        if not profile.is_teacher:
            messages.error(request, 'Access denied. Teachers only.')
            return redirect('admin_dashboard')
    except UserProfile.DoesNotExist:
        messages.error(request, 'Please complete your profile setup.')
        return redirect('profile_setup')
    
    # Get teacher's assignments
    all_assignments = TeacherAssignment.objects.filter(
        teacher=request.user,
        is_active=True
    ).select_related('class_assigned', 'subject', 'school')

    # Calculate completion rate
    total_possible_scores = 0
    completed_scores = 0

    # Get school context - prioritize school with most students
    school = None
    if all_assignments.exists():
        # Group assignments by school and calculate scores
        schools_data = {}
        for assignment in all_assignments:
            school_id = assignment.school.id
            if school_id not in schools_data:
                schools_data[school_id] = {
                    'school': assignment.school,
                    'assignments': [],
                    'class_teacher_count': 0,
                    'total_students': 0,
                    'classes': set()
                }

            schools_data[school_id]['assignments'].append(assignment)
            if assignment.is_class_teacher:
                schools_data[school_id]['class_teacher_count'] += 1
            schools_data[school_id]['classes'].add(assignment.class_assigned.id)

        # Calculate student counts for each school
        for school_data in schools_data.values():
            total_students = 0
            counted_classes = set()
            for assignment in school_data['assignments']:
                if assignment.class_assigned.id not in counted_classes:
                    student_count = Student.objects.filter(
                        current_class=assignment.class_assigned,
                        is_active=True
                    ).count()
                    total_students += student_count
                    counted_classes.add(assignment.class_assigned.id)
            school_data['total_students'] = total_students

        # Select school with highest score (prioritize students, then class teacher status, then assignments)
        best_school = None
        best_score = -1

        for school_data in schools_data.values():
            # Score: students * 1000 + class_teacher_assignments * 100 + total_assignments
            score = (school_data['total_students'] * 1000 +
                    school_data['class_teacher_count'] * 100 +
                    len(school_data['assignments']))

            if score > best_score:
                best_score = score
                best_school = school_data['school']

        school = best_school
    else:
        # If no active assignments, try to find any assignment for this teacher
        any_assignment = TeacherAssignment.objects.filter(teacher=request.user).first()
        if any_assignment:
            school = any_assignment.school
        else:
            # Last resort: get first school (for demo purposes)
            school = School.objects.first()

    # Filter assignments to only show those from the selected school
    assignments = all_assignments.filter(school=school) if school else all_assignments

    # Group assignments by class
    classes_data = {}
    total_students = 0
    unique_subjects = set()

    for assignment in assignments:
        class_name = assignment.class_assigned.name
        unique_subjects.add(assignment.subject.id)

        if class_name not in classes_data:
            # Use consistent student counting method
            student_count = Student.objects.filter(
                current_class=assignment.class_assigned,
                is_active=True
            ).count()
            classes_data[class_name] = {
                'class_obj': assignment.class_assigned,
                'subjects': [],
                'is_class_teacher': False,
                'student_count': student_count
            }
            total_students += student_count

        classes_data[class_name]['subjects'].append(assignment.subject)
        if assignment.is_class_teacher:
            classes_data[class_name]['is_class_teacher'] = True

    if school:
        for assignment in assignments:
            students_in_class = Student.objects.filter(
                current_class=assignment.class_assigned,
                is_active=True
            ).count()
            total_possible_scores += students_in_class

            # Count completed scores for this assignment
            completed_count = Score.objects.filter(
                teacher=request.user,
                subject=assignment.subject,
                student__current_class=assignment.class_assigned,
                term=school.current_term,
                academic_year=school.academic_year,
                is_submitted=True
            ).count()

            completed_scores += completed_count

    completion_rate = (completed_scores / total_possible_scores * 100) if total_possible_scores > 0 else 0

    context = {
        'classes_data': classes_data,
        'total_classes': len(classes_data),
        'total_subjects': len(unique_subjects),
        'total_students': total_students,
        'completion_rate': round(completion_rate, 1),
        'school': school,
    }
    
    return render(request, 'core/teacher/dashboard.html', context)

@login_required
def teacher_class_subjects(request, class_id):
    """Show subjects for a specific class assigned to teacher"""
    class_obj = get_object_or_404(Class, id=class_id)

    # Check if teacher has any assignment in this class
    teacher_assignments = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=class_obj,
        is_active=True
    ).select_related('subject')

    if not teacher_assignments.exists():
        messages.error(request, f'You do not have access to this class ({class_obj.name} in {class_obj.school.name}).')
        return redirect('teacher_dashboard')

    # Check if teacher is class teacher
    is_class_teacher = teacher_assignments.filter(is_class_teacher=True).exists()

    # If class teacher, create virtual assignments for all subjects in school
    if is_class_teacher:
        school = class_obj.school
        all_subjects = Subject.objects.filter(school=school, is_active=True).order_by('name')

        # Create a list of assignment-like objects for template compatibility
        assignments = []
        for subject in all_subjects:
            # Check if there's an actual assignment
            actual_assignment = teacher_assignments.filter(subject=subject).first()
            if actual_assignment:
                assignments.append(actual_assignment)
            else:
                # Create a virtual assignment object for template
                class VirtualAssignment:
                    def __init__(self, subject, is_class_teacher=True):
                        self.subject = subject
                        self.is_class_teacher = is_class_teacher
                        self.teacher = request.user
                        self.class_assigned = class_obj
                        self.school = school

                assignments.append(VirtualAssignment(subject, True))
    else:
        # Regular teacher - only show their assigned subjects
        assignments = teacher_assignments

    # Get students in this class
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')



    context = {
        'class_obj': class_obj,
        'assignments': assignments,
        'students': students,
        'student_count': students.count(),
        'school': class_obj.school,
        'is_class_teacher': is_class_teacher,
    }

    return render(request, 'core/teacher/class_subjects.html', context)

@teacher_required
def teacher_score_entry(request, class_id, subject_id):
    """Spreadsheet-style score entry form for a specific class and subject"""
    class_obj = get_object_or_404(Class, id=class_id)
    subject = get_object_or_404(Subject, id=subject_id)

    # Verify teacher has access - either assigned to this subject OR is class teacher
    assignment = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=class_obj,
        is_active=True
    ).filter(
        models.Q(subject=subject) | models.Q(is_class_teacher=True)
    ).first()

    if not assignment:
        messages.error(request, 'You do not have access to this subject in this class.')
        return redirect('teacher_dashboard')

    # Get students in this class
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')

    # Get current term and academic year
    school = class_obj.school
    current_term = school.current_term
    academic_year = school.academic_year

    # Get school's grading fields
    grading_fields = school.grading_fields.filter(is_active=True).order_by('order', 'name')

    if not grading_fields.exists():
        # Create default grading fields if none exist
        SchoolGradingField.create_default_fields(school)
        grading_fields = school.grading_fields.filter(is_active=True).order_by('order', 'name')

    # Get existing scores for these students
    existing_scores = {}
    scores = Score.objects.filter(
        student__in=students,
        subject=subject,
        term=current_term,
        academic_year=academic_year
    )

    for score in scores:
        existing_scores[score.student.id] = score.scores_data

    if request.method == 'POST':
        action = request.POST.get('action', 'save_draft')
        entry_mode = request.POST.get('entry_mode', 'raw_scores')

        # Create form with POST data
        form = SpreadsheetScoreForm(
            request.POST,
            students=students,
            grading_fields=grading_fields,
            existing_scores=existing_scores
        )

        if form.is_valid():
            scores_data = form.get_scores_data(students, grading_fields)

            with transaction.atomic():
                success_count = 0
                error_count = 0

                for student in students:
                    try:
                        student_scores = scores_data.get(student.id, {})

                        # Check if student has any non-zero scores
                        has_scores = any(float(score) > 0 for score in student_scores.values())
                        if not has_scores and action == 'submit':
                            # Don't submit empty scores, but allow saving drafts
                            continue

                        # Add entry mode to student scores for calculation
                        student_scores['_entry_mode'] = entry_mode

                        # Get or create score record
                        score, created = Score.objects.get_or_create(
                            student=student,
                            subject=subject,
                            teacher=request.user,
                            term=current_term,
                            academic_year=academic_year,
                            defaults={
                                'scores_data': student_scores,
                                'is_draft': action == 'save_draft'
                            }
                        )

                        if not created:
                            # Update existing score
                            score.scores_data = student_scores
                            score.is_draft = action == 'save_draft'
                            if action == 'submit':
                                score.is_submitted = True
                                score.submitted_at = timezone.now()
                            score.save()

                        # Calculate total and position
                        try:
                            score.calculate_total()
                            if action == 'submit':
                                score.calculate_position()
                                # Recalculate positions for all students in this subject
                                recalculate_subject_positions(subject, class_obj, current_term, academic_year)
                        except Exception as calc_error:
                            # Continue anyway, the score is saved
                            pass

                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        continue

                if success_count > 0:
                    if action == 'save_draft':
                        messages.success(request, f'Successfully saved draft scores for {success_count} students.')
                    else:
                        messages.success(request, f'Successfully submitted scores for {success_count} students.')

                if error_count > 0:
                    messages.warning(request, f'Failed to save scores for {error_count} students.')

                return redirect('teacher_score_entry', class_id=class_id, subject_id=subject_id)
        else:
            messages.error(request, 'Please correct the errors in the form.')
            # Show specific field errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field}: {error}")
    else:
        # Create form with existing data
        form = SpreadsheetScoreForm(
            students=students,
            grading_fields=grading_fields,
            existing_scores=existing_scores
        )

    # Calculate statistics
    total_students = students.count()
    submitted_count = Score.objects.filter(
        student__in=students,
        subject=subject,
        term=current_term,
        academic_year=academic_year,
        is_submitted=True
    ).count()

    draft_count = Score.objects.filter(
        student__in=students,
        subject=subject,
        term=current_term,
        academic_year=academic_year,
        is_draft=True,
        is_submitted=False
    ).count()

    # Get existing scores for template display
    scores = Score.objects.filter(
        student__in=students,
        subject=subject,
        term=current_term,
        academic_year=academic_year
    ).select_related('student')

    context = {
        'class_obj': class_obj,
        'subject': subject,
        'students': students,
        'grading_fields': grading_fields,
        'form': form,
        'school': school,
        'current_term': current_term,
        'academic_year': academic_year,
        'total_students': total_students,
        'submitted_count': submitted_count,
        'draft_count': draft_count,
        'completion_percentage': (submitted_count / total_students * 100) if total_students > 0 else 0,
        'scores': scores,
        'existing_scores': existing_scores,
    }

    return render(request, 'core/teacher/score_entry.html', context)

@login_required
def teacher_class_reports(request, class_id):
    """View reports for students in teacher's assigned class"""
    class_obj = get_object_or_404(Class, id=class_id)
    
    # Verify teacher has access to this class
    assignments = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=class_obj,
        is_active=True
    )
    
    if not assignments.exists():
        messages.error(request, 'You do not have access to this class.')
        return redirect('teacher_dashboard')
    
    # Get students and their results
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')
    
    school = class_obj.school
    current_term = school.current_term
    academic_year = school.academic_year
    
    # Get subjects taught by this teacher in this class
    teacher_subjects = [a.subject for a in assignments]
    
    students_data = []
    for student in students:
        # Get scores for subjects taught by this teacher
        scores = Score.objects.filter(
            student=student,
            subject__in=teacher_subjects,
            term=current_term,
            academic_year=academic_year
        ).select_related('subject')
        
        student_data = {
            'student': student,
            'scores': scores,
            'total_score': sum(score.total_score for score in scores),
            'average_score': sum(score.total_score for score in scores) / len(scores) if scores else 0,
            'subjects_count': scores.count()
        }
        students_data.append(student_data)
    
    # Sort by average score (highest first)
    students_data.sort(key=lambda x: x['average_score'], reverse=True)
    
    # Add positions
    for i, student_data in enumerate(students_data, 1):
        student_data['position'] = i
    
    context = {
        'class_obj': class_obj,
        'students_data': students_data,
        'teacher_subjects': teacher_subjects,
        'school': school,
        'current_term': current_term,
        'academic_year': academic_year,
    }
    
    return render(request, 'core/teacher/class_reports.html', context)

@login_required
def teacher_student_report(request, student_id):
    """View comprehensive report card for a specific student"""
    student = get_object_or_404(Student, id=student_id)

    # Verify teacher has access to this student's class
    assignments = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=student.current_class,
        is_active=True
    )

    if not assignments.exists():
        messages.error(request, 'You do not have access to this student.')
        return redirect('teacher_dashboard')

    school = student.school
    current_term = school.current_term
    academic_year = school.academic_year

    # Get school's grading fields configuration
    grading_fields = school.grading_fields.filter(is_active=True).order_by('order')
    if not grading_fields.exists():
        # Create default fields if none exist
        from core.models import SchoolGradingField
        SchoolGradingField.create_default_fields(school)
        grading_fields = school.grading_fields.filter(is_active=True).order_by('order')

    # Get ALL subjects for this student's class (not just teacher's subjects)
    all_class_subjects = Subject.objects.filter(
        school=school,
        is_active=True
    ).order_by('name')

    # Get subjects taught by this teacher
    teacher_subjects = [a.subject for a in assignments]

    # Get ALL scores for this student (from all teachers)
    all_scores = Score.objects.filter(
        student=student,
        term=current_term,
        academic_year=academic_year
    ).select_related('subject')

    # Create a dictionary for quick score lookup
    scores_dict = {score.subject.id: score for score in all_scores}

    # Build comprehensive subject data
    subject_data = []
    total_score = 0
    subjects_with_scores = 0

    for subject in all_class_subjects:
        score_obj = scores_dict.get(subject.id)

        if score_obj:
            # Subject has scores
            # Get individual field scores
            field_scores = {}
            for field in grading_fields:
                field_scores[field.code] = score_obj.get_score_for_field(field.code)

            # Get remarks based on grade
            if score_obj.total_score >= 80:
                remarks = 'EXCELLENT'
            elif score_obj.total_score >= 70:
                remarks = 'VERY GOOD'
            elif score_obj.total_score >= 60:
                remarks = 'GOOD'
            elif score_obj.total_score >= 50:
                remarks = 'SATISFACTORY'
            else:
                remarks = 'NEEDS IMPROVEMENT'

            subject_info = {
                'subject': subject,
                'score': score_obj,
                'has_data': True,
                'is_teacher_subject': subject in teacher_subjects,
                'total_score': score_obj.total_score,
                'grade': score_obj.grade,
                'position': score_obj.position,
                'remarks': remarks,
                'field_scores': field_scores,
                'status': 'Completed' if score_obj.is_submitted else 'Draft'
            }
            total_score += score_obj.total_score
            subjects_with_scores += 1
        else:
            # Subject has no scores
            subject_info = {
                'subject': subject,
                'score': None,
                'has_data': False,
                'is_teacher_subject': subject in teacher_subjects,
                'total_score': 0,
                'grade': '-',
                'position': '-',
                'remarks': '-',
                'field_scores': {},
                'status': 'Not Available'
            }

        subject_data.append(subject_info)

    # Calculate average
    average_score = (total_score / subjects_with_scores) if subjects_with_scores > 0 else 0

    # Calculate class position (based on available scores)
    class_students = Student.objects.filter(
        current_class=student.current_class,
        is_active=True
    )

    # Get term results for position calculation
    from core.models import StudentTermResult
    try:
        term_result = StudentTermResult.objects.get(
            student=student,
            term=current_term,
            academic_year=academic_year
        )
        class_position = term_result.position
        total_students = class_students.count()
    except StudentTermResult.DoesNotExist:
        class_position = 0
        total_students = class_students.count()

    # Convert student profile picture to base64 for better display
    student_photo_base64 = None
    if student.profile_picture:
        try:
            import base64
            from django.conf import settings
            import os

            # Get the full file path
            photo_path = os.path.join(settings.MEDIA_ROOT, student.profile_picture.name)
            if os.path.exists(photo_path):
                with open(photo_path, 'rb') as image_file:
                    image_data = image_file.read()
                    student_photo_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            print(f"Error converting student photo to base64: {e}")
            student_photo_base64 = None

    # Convert school logo to base64 for better display
    school_logo_base64 = None
    if school.logo:
        try:
            import base64
            from django.conf import settings
            import os

            # Get the full file path
            logo_path = os.path.join(settings.MEDIA_ROOT, school.logo.name)
            if os.path.exists(logo_path):
                with open(logo_path, 'rb') as image_file:
                    image_data = image_file.read()
                    school_logo_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            print(f"Error converting school logo to base64: {e}")
            school_logo_base64 = None

    # Generate document security hash for verification
    import hashlib
    from datetime import datetime
    security_string = f"{school.id}-{student.id}-{current_term}-{academic_year}-{total_score}-{datetime.now().strftime('%Y%m%d')}"
    document_hash = hashlib.md5(security_string.encode()).hexdigest()[:8].upper()

    context = {
        'student': student,
        'subject_data': subject_data,
        'teacher_subjects': teacher_subjects,
        'school': school,
        'current_term': current_term,
        'academic_year': academic_year,
        'grading_fields': grading_fields,
        'total_score': total_score,
        'average_score': round(average_score, 2),
        'subjects_with_scores': subjects_with_scores,
        'total_subjects': all_class_subjects.count(),
        'class_position': class_position,
        'total_students': total_students,
        'completion_percentage': round((subjects_with_scores / all_class_subjects.count() * 100), 1) if all_class_subjects.count() > 0 else 0,
        'template': school.report_template,  # Add template for consistency
        'student_photo_base64': student_photo_base64,
        'school_logo_base64': school_logo_base64,
        'document_hash': document_hash,
    }

    return render(request, 'core/teacher/student_report_card.html', context)


@teacher_required
def teacher_submitted_scores(request, class_id, subject_id):
    """View submitted scores for a specific class and subject"""
    class_obj = get_object_or_404(Class, id=class_id)
    subject = get_object_or_404(Subject, id=subject_id)

    # Verify teacher has access
    assignment = get_object_or_404(
        TeacherAssignment,
        teacher=request.user,
        class_assigned=class_obj,
        subject=subject,
        is_active=True
    )

    # Get students in this class
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')

    # Get current term and academic year
    school = class_obj.school
    current_term = school.current_term
    academic_year = school.academic_year

    # Get school's grading fields
    grading_fields = school.grading_fields.filter(is_active=True).order_by('order', 'name')

    # Get submitted scores for these students
    scores = Score.objects.filter(
        student__in=students,
        subject=subject,
        term=current_term,
        academic_year=academic_year,
        is_submitted=True
    ).select_related('student')

    # Organize scores by student
    student_scores = {}
    for score in scores:
        student_scores[score.student.id] = {
            'score': score,
            'scores_data': score.scores_data,
            'total_score': score.total_score,
            'grade': score.grade,
            'submitted_at': score.updated_at
        }

    # Calculate statistics
    total_students = students.count()
    submitted_count = len(student_scores)
    pending_count = total_students - submitted_count

    context = {
        'class_obj': class_obj,
        'subject': subject,
        'students': students,
        'grading_fields': grading_fields,
        'student_scores': student_scores,
        'school': school,
        'current_term': current_term,
        'academic_year': academic_year,
        'assignment': assignment,
        'total_students': total_students,
        'submitted_count': submitted_count,
        'pending_count': pending_count,
        'completion_percentage': (submitted_count / total_students * 100) if total_students > 0 else 0,
    }

    return render(request, 'core/teacher/submitted_scores.html', context)


@teacher_required
def teacher_export_scores(request, class_id, subject_id):
    """Export scores for a specific class and subject as Excel"""
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    from django.http import HttpResponse, JsonResponse

    class_obj = get_object_or_404(Class, id=class_id)
    subject = get_object_or_404(Subject, id=subject_id)

    # Verify teacher has access
    assignment = get_object_or_404(
        TeacherAssignment,
        teacher=request.user,
        class_assigned=class_obj,
        subject=subject,
        is_active=True
    )

    # Get students and scores
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')

    school = class_obj.school
    current_term = school.current_term
    academic_year = school.academic_year
    grading_fields = school.grading_fields.filter(is_active=True).order_by('order', 'name')

    scores = Score.objects.filter(
        student__in=students,
        subject=subject,
        term=current_term,
        academic_year=academic_year
    ).select_related('student')

    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = f"{class_obj.name} - {subject.name}"

    # Header styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")

    # Add title
    ws.merge_cells('A1:F1')
    ws['A1'] = f"{school.name} - {class_obj.name} - {subject.name}"
    ws['A1'].font = Font(bold=True, size=14)
    ws['A1'].alignment = center_alignment

    # Add term info
    ws.merge_cells('A2:F2')
    ws['A2'] = f"{current_term} {academic_year}"
    ws['A2'].font = Font(bold=True)
    ws['A2'].alignment = center_alignment

    # Headers
    row = 4
    headers = ['Student Name', 'Student ID']
    for field in grading_fields:
        headers.append(f"{field.name} ({field.max_score})")
    headers.extend(['Total Score', 'Grade', 'Status'])

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment

    # Student data
    score_dict = {score.student.id: score for score in scores}

    for student in students:
        row += 1
        ws.cell(row=row, column=1, value=student.full_name)
        ws.cell(row=row, column=2, value=student.student_id)

        col = 3
        score_obj = score_dict.get(student.id)

        if score_obj:
            # Individual field scores
            for field in grading_fields:
                field_score = score_obj.scores_data.get(field.code, 0)
                ws.cell(row=row, column=col, value=field_score)
                col += 1

            # Total and grade
            ws.cell(row=row, column=col, value=score_obj.total_score)
            ws.cell(row=row, column=col+1, value=score_obj.grade)
            ws.cell(row=row, column=col+2, value="Submitted" if score_obj.is_submitted else "Draft")
        else:
            # Empty scores
            for field in grading_fields:
                ws.cell(row=row, column=col, value=0)
                col += 1
            ws.cell(row=row, column=col, value=0)
            ws.cell(row=row, column=col+1, value="-")
            ws.cell(row=row, column=col+2, value="Not Entered")

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        # Handle merged cells by checking if column_letter exists
        try:
            column_letter = column[0].column_letter
        except AttributeError:
            # Skip merged cells
            continue

        for cell in column:
            try:
                if hasattr(cell, 'value') and cell.value is not None:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    filename = f"{class_obj.name}_{subject.name}_{current_term}_{academic_year}.xlsx"
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    wb.save(response)
    return response


@login_required
def class_teacher_management(request, class_id):
    """Class teacher management for attendance and comments"""
    class_obj = get_object_or_404(Class, id=class_id)

    # Verify teacher is the class teacher for this class
    class_teacher_assignment = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=class_obj,
        is_class_teacher=True,
        is_active=True
    ).first()

    if not class_teacher_assignment:
        messages.error(request, 'You are not the class teacher for this class.')
        return redirect('teacher_dashboard')

    school = class_obj.school
    current_term = school.current_term
    academic_year = school.academic_year

    # Get all students in the class
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')

    # Get or create term results for each student
    student_results = []
    for student in students:
        result, created = StudentTermResult.objects.get_or_create(
            student=student,
            term=current_term,
            academic_year=academic_year,
            defaults={
                'attendance_present': 0,
                'attendance_total': 0,
                'teacher_comment': '',
            }
        )
        student_results.append({
            'student': student,
            'result': result,
        })

    if request.method == 'POST':
        # Handle attendance and comments update
        for student_result in student_results:
            student = student_result['student']
            result = student_result['result']

            # Update attendance
            attendance_present = request.POST.get(f'attendance_present_{student.id}')
            attendance_total = request.POST.get(f'attendance_total_{student.id}')
            teacher_comment = request.POST.get(f'teacher_comment_{student.id}')

            # Update comment fields
            conduct_comment = request.POST.get(f'conduct_comment_{student.id}')
            attitude_comment = request.POST.get(f'attitude_comment_{student.id}')
            interest_comment = request.POST.get(f'interest_comment_{student.id}')
            promoted_to = request.POST.get(f'promoted_to_{student.id}')

            if attendance_present is not None:
                result.attendance_present = int(attendance_present) if attendance_present else 0
            if attendance_total is not None:
                result.attendance_total = int(attendance_total) if attendance_total else 0
            if teacher_comment is not None:
                result.teacher_comment = teacher_comment.strip()
            if conduct_comment is not None:
                result.conduct_comment = conduct_comment.strip()
            if attitude_comment is not None:
                result.attitude_comment = attitude_comment.strip()
            if interest_comment is not None:
                result.interest_comment = interest_comment.strip()
            if promoted_to is not None:
                result.promoted_to = promoted_to.strip()

            result.save()

        messages.success(request, 'Attendance and comments updated successfully!')
        return redirect('class_teacher_management', class_id=class_id)

    context = {
        'class_obj': class_obj,
        'school': school,
        'current_term': current_term,
        'academic_year': academic_year,
        'student_results': student_results,
        'is_class_teacher': True,
    }

    return render(request, 'core/teacher/class_teacher_management.html', context)


@login_required
def teacher_all_subjects_access(request, class_id):
    """Allow class teachers to access all subjects for their class"""
    class_obj = get_object_or_404(Class, id=class_id)

    # Check if teacher is class teacher OR has any assignment in this class
    teacher_assignments = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=class_obj,
        is_active=True
    )

    is_class_teacher = teacher_assignments.filter(is_class_teacher=True).exists()

    if not teacher_assignments.exists():
        messages.error(request, 'You do not have access to this class.')
        return redirect('teacher_dashboard')

    school = class_obj.school

    # Get ALL subjects for this school (class teachers can access all)
    if is_class_teacher:
        subjects = Subject.objects.filter(
            school=school,
            is_active=True
        ).order_by('name')
    else:
        # Regular teachers only see their assigned subjects
        assigned_subject_ids = teacher_assignments.values_list('subject_id', flat=True)
        subjects = Subject.objects.filter(
            id__in=assigned_subject_ids,
            is_active=True
        ).order_by('name')

    # Get students in this class
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')

    context = {
        'class_obj': class_obj,
        'school': school,
        'subjects': subjects,
        'students': students,
        'is_class_teacher': is_class_teacher,
        'teacher_assignments': teacher_assignments,
    }

    return render(request, 'core/teacher/all_subjects_access.html', context)


@login_required
def student_management(request, class_id):
    """Enhanced student management interface for class teachers"""
    class_obj = get_object_or_404(Class, id=class_id)

    # Verify teacher is the class teacher for this class
    class_teacher_assignment = TeacherAssignment.objects.filter(
        teacher=request.user,
        class_assigned=class_obj,
        is_class_teacher=True,
        is_active=True
    ).first()

    if not class_teacher_assignment:
        messages.error(request, 'You are not the class teacher for this class.')
        return redirect('teacher_dashboard')

    school = class_obj.school
    current_term = school.current_term
    academic_year = school.academic_year

    # Get all students in the class
    students = Student.objects.filter(
        current_class=class_obj,
        is_active=True
    ).order_by('first_name', 'last_name')

    # Get or create term results for each student
    student_results = []
    for student in students:
        result, created = StudentTermResult.objects.get_or_create(
            student=student,
            term=current_term,
            academic_year=academic_year,
            defaults={
                'attendance_present': 0,
                'attendance_total': 0,
                'teacher_comment': '',
                'conduct_comment': '',
                'attitude_comment': '',
                'interest_comment': '',
                'promoted_to': '',
            }
        )
        student_results.append({
            'student': student,
            'result': result,
        })

    if request.method == 'POST':
        # Handle AJAX requests for individual student updates
        if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
            student_id = request.POST.get('student_id')
            if student_id:
                try:
                    student = Student.objects.get(id=student_id, current_class=class_obj)
                    result, created = StudentTermResult.objects.get_or_create(
                        student=student,
                        term=current_term,
                        academic_year=academic_year
                    )

                    # Update fields
                    result.attendance_present = int(request.POST.get('attendance_present', 0))
                    result.attendance_total = int(request.POST.get('attendance_total', 0))
                    result.conduct_comment = request.POST.get('conduct_comment', '').strip()
                    result.attitude_comment = request.POST.get('attitude_comment', '').strip()
                    result.interest_comment = request.POST.get('interest_comment', '').strip()
                    result.promoted_to = request.POST.get('promoted_to', '').strip()
                    result.teacher_comment = request.POST.get('teacher_comment', '').strip()

                    # Handle custom promotion flag (for frontend logic)
                    custom_promotion = request.POST.get('custom_promotion') == 'true'

                    result.save()

                    return JsonResponse({
                        'success': True,
                        'message': f'Data saved successfully for {student.first_name} {student.last_name}'
                    })
                except Exception as e:
                    return JsonResponse({'success': False, 'error': str(e)})

        # Handle bulk updates (save all)
        for student_result in student_results:
            student = student_result['student']
            result = student_result['result']

            # Update attendance
            attendance_present = request.POST.get(f'attendance_present_{student.id}')
            attendance_total = request.POST.get(f'attendance_total_{student.id}')
            teacher_comment = request.POST.get(f'teacher_comment_{student.id}')

            # Update comment fields
            conduct_comment = request.POST.get(f'conduct_comment_{student.id}')
            attitude_comment = request.POST.get(f'attitude_comment_{student.id}')
            interest_comment = request.POST.get(f'interest_comment_{student.id}')
            promoted_to = request.POST.get(f'promoted_to_{student.id}')

            if attendance_present is not None:
                result.attendance_present = int(attendance_present) if attendance_present else 0
            if attendance_total is not None:
                result.attendance_total = int(attendance_total) if attendance_total else 0
            if teacher_comment is not None:
                result.teacher_comment = teacher_comment.strip()
            if conduct_comment is not None:
                result.conduct_comment = conduct_comment.strip()
            if attitude_comment is not None:
                result.attitude_comment = attitude_comment.strip()
            if interest_comment is not None:
                result.interest_comment = interest_comment.strip()
            if promoted_to is not None:
                result.promoted_to = promoted_to.strip()

            result.save()

        messages.success(request, 'All student data updated successfully!')
        return redirect('student_management', class_id=class_id)

    context = {
        'class_obj': class_obj,
        'school': school,
        'current_term': current_term,
        'academic_year': academic_year,
        'student_results': student_results,
        'is_class_teacher': True,
    }

    return render(request, 'core/teacher/student_management.html', context)
