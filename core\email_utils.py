"""
Email utilities for sending school-branded emails using centralized SMTP configuration.
Site admin configures SMTP, but emails are sent with school branding.
"""

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings


def send_school_email(school, subject, template_name, context, recipient_email, fail_silently=False):
    """
    Send an email with school branding using centralized SMTP configuration.
    
    Args:
        school: School instance for branding
        subject: Email subject (will be prefixed with school name)
        template_name: Template name (without .html/.txt extension)
        context: Template context dictionary
        recipient_email: Recipient email address
        fail_silently: Whether to fail silently on errors
    
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Add school context
        email_context = {
            **context,
            'school': school,
            'school_logo_url': school.logo.url if school.logo else None,
        }
        
        # Render email templates
        html_template = f'core/emails/{template_name}.html'
        text_template = f'core/emails/{template_name}.txt'
        
        html_message = render_to_string(html_template, email_context)
        plain_message = render_to_string(text_template, email_context)
        
        # Get site settings for SMTP configuration
        from .models import SiteSettings
        site_settings = SiteSettings.get_settings()

        # Create school-branded from email using site SMTP settings
        from_email = f"{school.name} <{site_settings.email_host_user}>"
        
        # Send email using centralized SMTP but with school branding
        send_mail(
            subject=f"{school.name} - {subject}",
            message=plain_message,
            from_email=from_email,
            recipient_list=[recipient_email],
            html_message=html_message,
            fail_silently=fail_silently,
        )
        
        return True
        
    except Exception as e:
        print(f"Failed to send email to {recipient_email}: {e}")
        if not fail_silently:
            raise
        return False


def send_teacher_welcome_email(school, user, username, password, login_url, admin_user):
    """
    Send welcome email to new teacher with school branding.
    """
    # Check if school has welcome emails enabled
    if not school.settings.send_welcome_emails:
        return False
    
    context = {
        'user': user,
        'username': username,
        'password': password,
        'login_url': login_url,
        'admin_user': admin_user,
    }
    
    return send_school_email(
        school=school,
        subject="Welcome - Your Teacher Account Created",
        template_name="teacher_welcome",
        context=context,
        recipient_email=user.email,
        fail_silently=True
    )


def send_password_reset_email(school, user, new_password, login_url):
    """
    Send password reset email with school branding.
    """
    # Check if school has password reset emails enabled
    if not school.settings.send_password_reset_emails:
        return False
    
    context = {
        'user': user,
        'new_password': new_password,
        'login_url': login_url,
    }
    
    return send_school_email(
        school=school,
        subject="Password Reset",
        template_name="password_reset",
        context=context,
        recipient_email=user.email,
        fail_silently=True
    )


def send_grade_notification_email(school, teacher, subject_name, class_name):
    """
    Send grade submission notification email.
    """
    # Check if school has grade notifications enabled
    if not hasattr(school.settings, 'send_grade_notifications') or not school.settings.send_grade_notifications:
        return False
    
    context = {
        'teacher': teacher,
        'subject_name': subject_name,
        'class_name': class_name,
    }
    
    return send_school_email(
        school=school,
        subject="Grade Submission Confirmation",
        template_name="grade_notification",
        context=context,
        recipient_email=teacher.email,
        fail_silently=True
    )
